[{"layout_dets": [{"category_id": 5, "poly": [169, 108, 1503, 108, 1503, 263, 169, 263], "score": 0.96, "html": "<table><tr><td rowspan=\"2\">鹏翎 INEI</td><td colspan=\"2\">天津鹏翎集团股份有限公司</td></tr><tr><td>需求说明-SOR</td><td>No:PL/SH-E001-AA-2024</td></tr></table>"}, {"category_id": 2, "poly": [456, 2209, 1387, 2209, 1387, 2257, 456, 2257], "score": 0.903}, {"category_id": 1, "poly": [288, 1240, 1361, 1240, 1361, 1300, 288, 1300], "score": 0.433}, {"category_id": 0, "poly": [460, 1096, 1186, 1096, 1186, 1163, 460, 1163], "score": 0.38}, {"category_id": 1, "poly": [460, 1096, 1186, 1096, 1186, 1163, 460, 1163], "score": 0.349}, {"category_id": 2, "poly": [923, 637, 1463, 637, 1463, 1015, 923, 1015], "score": 0.113}, {"category_id": 15, "poly": [453.0, 2204.0, 1286.0, 2204.0, 1286.0, 2262.0, 453.0, 2262.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1309.0, 2216.0, 1390.0, 2216.0, 1390.0, 2260.0, 1309.0, 2260.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [458.0, 1091.0, 1185.0, 1091.0, 1185.0, 1168.0, 458.0, 1168.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [936.0, 899.0, 970.0, 899.0, 970.0, 933.0, 936.0, 933.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [974.0, 947.0, 1057.0, 947.0, 1057.0, 1019.0, 974.0, 1019.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [957.25, 729.0, 1540.25, 729.0, 1540.25, 931.5, 957.25, 931.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [293.0, 1230.0, 1362.0, 1230.0, 1362.0, 1306.0, 293.0, 1306.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [458.0, 1091.0, 1185.0, 1091.0, 1185.0, 1168.0, 458.0, 1168.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 0, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [209, 724, 1449, 724, 1449, 1245, 209, 1245], "score": 0.979, "html": "<table><tr><td>版本</td><td>作者</td><td>日期</td><td>详细说明</td></tr><tr><td>A</td><td>Peter <PERSON></td><td>2025/1/10</td><td>初版释放。</td></tr><tr><td>B</td><td>Peter <PERSON></td><td>2025/3/25</td><td>1.根据客户端输入的LN信号矩阵表更新信号表；</td></tr><tr><td>C</td><td>Peter <PERSON></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr></table>"}, {"category_id": 5, "poly": [208, 335, 1358, 335, 1358, 585, 208, 585], "score": 0.978, "html": "<table><tr><td>审批信息</td><td>姓名</td><td>职位</td><td>日期</td></tr><tr><td>编制</td><td></td><td></td><td></td></tr><tr><td>审评</td><td></td><td></td><td></td></tr><tr><td>批注</td><td></td><td></td><td></td></tr></table>"}, {"category_id": 2, "poly": [459, 2221, 1236, 2221, 1236, 2262, 459, 2262], "score": 0.927}, {"category_id": 2, "poly": [1366, 2225, 1467, 2225, 1467, 2258, 1366, 2258], "score": 0.915}, {"category_id": 2, "poly": [187, 2224, 317, 2224, 317, 2258, 187, 2258], "score": 0.905}, {"category_id": 5, "poly": [154, 108, 1493, 108, 1493, 246, 154, 246], "score": 0.837, "html": "<table><tr><td>鹏翎 NGI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 1, "poly": [194, 263, 358, 263, 358, 309, 194, 309], "score": 0.662}, {"category_id": 6, "poly": [194, 653, 356, 653, 356, 698, 194, 698], "score": 0.607}, {"category_id": 2, "poly": [156, 112, 351, 112, 351, 244, 156, 244], "score": 0.574}, {"category_id": 6, "poly": [194, 263, 358, 263, 358, 309, 194, 309], "score": 0.256}, {"category_id": 1, "poly": [194, 653, 356, 653, 356, 698, 194, 698], "score": 0.225}, {"category_id": 0, "poly": [194, 653, 356, 653, 356, 698, 194, 698], "score": 0.115}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.28, "latex": "\\circledcirc"}, {"category_id": 15, "poly": [493.0, 2219.0, 1237.0, 2219.0, 1237.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1359.0, 2217.0, 1475.0, 2217.0, 1475.0, 2265.0, 1359.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [183.0, 2220.0, 321.0, 2220.0, 321.0, 2268.0, 183.0, 2268.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [190.0, 652.0, 360.0, 652.0, 360.0, 703.0, 190.0, 703.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [218.0, 188.0, 285.0, 188.0, 285.0, 233.0, 218.0, 233.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [189.0, 260.0, 361.0, 260.0, 361.0, 314.0, 189.0, 314.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [190.0, 652.0, 360.0, 652.0, 360.0, 703.0, 190.0, 703.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [189.0, 260.0, 361.0, 260.0, 361.0, 314.0, 189.0, 314.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [190.0, 652.0, 360.0, 652.0, 360.0, 703.0, 190.0, 703.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 1, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 2, "poly": [458, 2221, 1236, 2221, 1236, 2262, 458, 2262], "score": 0.922}, {"category_id": 0, "poly": [785, 265, 873, 265, 873, 315, 785, 315], "score": 0.916}, {"category_id": 2, "poly": [1366, 2224, 1467, 2224, 1467, 2258, 1366, 2258], "score": 0.914}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2258, 187, 2258], "score": 0.892}, {"category_id": 1, "poly": [147, 328, 1507, 328, 1507, 1719, 147, 1719], "score": 0.557}, {"category_id": 5, "poly": [154, 109, 1495, 109, 1495, 246, 154, 246], "score": 0.385, "html": "<table><tr><td>鹏翎</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 2, "poly": [154, 109, 1495, 109, 1495, 246, 154, 246], "score": 0.319}, {"category_id": 2, "poly": [155, 111, 348, 111, 348, 244, 155, 244], "score": 0.165}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.28, "latex": "\\circledcirc"}, {"category_id": 15, "poly": [493.0, 2219.0, 1237.0, 2219.0, 1237.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [780.0, 262.0, 881.0, 262.0, 881.0, 323.0, 780.0, 323.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1358.0, 2215.0, 1476.0, 2215.0, 1476.0, 2267.0, 1358.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2222.0, 320.0, 2222.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1338.0, 115.0, 1497.0, 115.0, 1497.0, 170.0, 1338.0, 170.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [217.0, 188.0, 284.0, 188.0, 284.0, 231.0, 217.0, 231.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [778.0, 186.0, 1489.0, 186.0, 1489.0, 228.0, 778.0, 228.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 188.0, 285.0, 188.0, 285.0, 233.0, 219.0, 233.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [147.0, 335.0, 302.0, 335.0, 302.0, 381.0, 147.0, 381.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1450.0, 346.0, 1500.0, 346.0, 1500.0, 374.0, 1450.0, 374.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [173.0, 388.0, 357.0, 388.0, 357.0, 433.0, 173.0, 433.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1467.0, 396.0, 1504.0, 396.0, 1504.0, 429.0, 1467.0, 429.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [169.0, 424.0, 353.0, 424.0, 353.0, 466.0, 169.0, 466.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1467.0, 432.0, 1504.0, 432.0, 1504.0, 464.0, 1467.0, 464.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [169.0, 460.0, 309.0, 460.0, 309.0, 506.0, 169.0, 506.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1459.0, 467.0, 1505.0, 467.0, 1505.0, 503.0, 1459.0, 503.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [169.0, 497.0, 377.0, 497.0, 377.0, 539.0, 169.0, 539.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1463.0, 506.0, 1502.0, 506.0, 1502.0, 537.0, 1463.0, 537.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 548.0, 497.0, 548.0, 497.0, 592.0, 151.0, 592.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1441.0, 558.0, 1504.0, 558.0, 1504.0, 590.0, 1441.0, 590.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [174.0, 604.0, 363.0, 604.0, 363.0, 645.0, 174.0, 645.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1467.0, 610.0, 1504.0, 610.0, 1504.0, 643.0, 1467.0, 643.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [171.0, 637.0, 371.0, 637.0, 371.0, 682.0, 171.0, 682.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1464.0, 641.0, 1507.0, 641.0, 1507.0, 680.0, 1464.0, 680.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [195.0, 676.0, 453.0, 676.0, 453.0, 716.0, 195.0, 716.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1459.0, 679.0, 1505.0, 679.0, 1505.0, 714.0, 1459.0, 714.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [195.0, 710.0, 434.0, 710.0, 434.0, 750.0, 195.0, 750.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1463.0, 716.0, 1505.0, 716.0, 1505.0, 752.0, 1463.0, 752.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [192.0, 742.0, 446.0, 742.0, 446.0, 791.0, 192.0, 791.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1464.0, 755.0, 1502.0, 755.0, 1502.0, 786.0, 1464.0, 786.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [174.0, 784.0, 390.0, 784.0, 390.0, 825.0, 174.0, 825.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1475.0, 792.0, 1500.0, 792.0, 1500.0, 820.0, 1475.0, 820.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [191.0, 819.0, 403.0, 819.0, 403.0, 860.0, 191.0, 860.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1475.0, 828.0, 1500.0, 828.0, 1500.0, 856.0, 1475.0, 856.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [174.0, 856.0, 409.0, 856.0, 409.0, 896.0, 174.0, 896.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1475.0, 864.0, 1500.0, 864.0, 1500.0, 891.0, 1475.0, 891.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1118.0, 879.0, 1161.0, 879.0, 1161.0, 899.0, 1118.0, 899.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [173.0, 891.0, 353.0, 891.0, 353.0, 932.0, 173.0, 932.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1470.0, 898.0, 1504.0, 898.0, 1504.0, 930.0, 1470.0, 930.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [147.0, 940.0, 330.0, 940.0, 330.0, 989.0, 147.0, 989.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [973.0, 955.0, 1060.0, 955.0, 1060.0, 991.0, 973.0, 991.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1474.0, 954.0, 1500.0, 954.0, 1500.0, 982.0, 1474.0, 982.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [171.0, 996.0, 497.0, 996.0, 497.0, 1039.0, 171.0, 1039.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1459.0, 1002.0, 1505.0, 1002.0, 1505.0, 1037.0, 1459.0, 1037.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [173.0, 1034.0, 339.0, 1034.0, 339.0, 1075.0, 173.0, 1075.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1452.0, 1036.0, 1507.0, 1036.0, 1507.0, 1076.0, 1452.0, 1076.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [194.0, 1072.0, 457.0, 1072.0, 457.0, 1109.0, 194.0, 1109.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1455.0, 1072.0, 1508.0, 1072.0, 1508.0, 1112.0, 1455.0, 1112.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [194.0, 1108.0, 457.0, 1108.0, 457.0, 1144.0, 194.0, 1144.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [783.0, 1106.0, 888.0, 1106.0, 888.0, 1202.0, 783.0, 1202.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1455.0, 1107.0, 1507.0, 1107.0, 1507.0, 1148.0, 1455.0, 1148.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [196.0, 1142.0, 402.0, 1142.0, 402.0, 1182.0, 196.0, 1182.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1452.0, 1143.0, 1507.0, 1143.0, 1507.0, 1183.0, 1452.0, 1183.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [173.0, 1176.0, 427.0, 1176.0, 427.0, 1253.0, 173.0, 1253.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [722.0, 1201.0, 825.0, 1201.0, 825.0, 1297.0, 722.0, 1297.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [898.0, 1196.0, 1003.0, 1196.0, 1003.0, 1255.0, 898.0, 1255.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1445.0, 1179.0, 1507.0, 1179.0, 1507.0, 1255.0, 1445.0, 1255.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [194.0, 1250.0, 432.0, 1250.0, 432.0, 1287.0, 194.0, 1287.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1458.0, 1253.0, 1505.0, 1253.0, 1505.0, 1291.0, 1458.0, 1291.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [171.0, 1286.0, 413.0, 1286.0, 413.0, 1323.0, 171.0, 1323.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1455.0, 1289.0, 1505.0, 1289.0, 1505.0, 1325.0, 1455.0, 1325.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [171.0, 1322.0, 466.0, 1322.0, 466.0, 1359.0, 171.0, 1359.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1450.0, 1326.0, 1505.0, 1326.0, 1505.0, 1361.0, 1450.0, 1361.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [169.0, 1356.0, 358.0, 1356.0, 358.0, 1398.0, 169.0, 1398.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1437.0, 1357.0, 1508.0, 1357.0, 1508.0, 1401.0, 1437.0, 1401.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [174.0, 1393.0, 358.0, 1393.0, 358.0, 1434.0, 174.0, 1434.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1433.0, 1396.0, 1507.0, 1396.0, 1507.0, 1435.0, 1433.0, 1435.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [196.0, 1432.0, 637.0, 1432.0, 637.0, 1471.0, 196.0, 1471.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1452.0, 1430.0, 1507.0, 1430.0, 1507.0, 1472.0, 1452.0, 1472.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [196.0, 1468.0, 640.0, 1468.0, 640.0, 1507.0, 196.0, 1507.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [645.0, 1479.0, 698.0, 1479.0, 698.0, 1507.0, 645.0, 1507.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1452.0, 1468.0, 1507.0, 1468.0, 1507.0, 1508.0, 1452.0, 1508.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [195.0, 1500.0, 564.0, 1500.0, 564.0, 1544.0, 195.0, 1544.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1452.0, 1503.0, 1507.0, 1503.0, 1507.0, 1544.0, 1452.0, 1544.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [196.0, 1538.0, 438.0, 1538.0, 438.0, 1578.0, 196.0, 1578.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1448.0, 1539.0, 1507.0, 1539.0, 1507.0, 1578.0, 1448.0, 1578.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [173.0, 1572.0, 519.0, 1572.0, 519.0, 1615.0, 173.0, 1615.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [578.0, 1597.0, 597.0, 1597.0, 597.0, 1609.0, 578.0, 1609.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1448.0, 1576.0, 1507.0, 1576.0, 1507.0, 1615.0, 1448.0, 1615.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [174.0, 1611.0, 363.0, 1611.0, 363.0, 1651.0, 174.0, 1651.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1445.0, 1611.0, 1507.0, 1611.0, 1507.0, 1651.0, 1445.0, 1651.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [195.0, 1646.0, 459.0, 1646.0, 459.0, 1687.0, 195.0, 1687.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1455.0, 1649.0, 1504.0, 1649.0, 1504.0, 1685.0, 1455.0, 1685.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [174.0, 1682.0, 451.0, 1682.0, 451.0, 1722.0, 174.0, 1722.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1445.0, 1684.0, 1505.0, 1684.0, 1505.0, 1724.0, 1445.0, 1724.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1098.0, 881.5, 1247.0, 881.5, 1247.0, 1005.5, 1098.0, 1005.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [990.75, 972.0, 1191.75, 972.0, 1191.75, 1127.5, 990.75, 1127.5], "score": 1.0, "text": ""}], "page_info": {"page_no": 2, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [250, 772, 1402, 772, 1402, 1162, 250, 1162], "score": 0.982, "html": "<table><tr><td>序号</td><td>缩写</td><td>解释</td></tr><tr><td>1</td><td>EWV</td><td>Electronic Water Valve（电子水阀）</td></tr><tr><td>2</td><td>LIN</td><td>Local Interconnect Network（局域互联网络）</td></tr><tr><td>3</td><td>VBAT</td><td>Voltage of Battery（蓄电池电压）</td></tr><tr><td>4</td><td>SOR</td><td>Statement of Requirements（需求说明）</td></tr><tr><td>5</td><td>BLDC</td><td>Brushless Direct Current Motor（无刷直流电机）</td></tr></table>"}, {"category_id": 5, "poly": [251, 1272, 1402, 1272, 1402, 1470, 251, 1470], "score": 0.979, "html": "<table><tr><td>序号</td><td>文件名</td><td>文件编号</td></tr><tr><td>1</td><td>《SAIC_TWV_3WAY_LIN_Matrix_A1-2025.03.10》</td><td></td></tr><tr><td>2</td><td></td><td></td></tr></table>"}, {"category_id": 1, "poly": [154, 1671, 1489, 1671, 1489, 1767, 154, 1767], "score": 0.972}, {"category_id": 0, "poly": [155, 1538, 606, 1538, 606, 1592, 155, 1592], "score": 0.942}, {"category_id": 0, "poly": [155, 556, 388, 556, 388, 603, 155, 603], "score": 0.939}, {"category_id": 0, "poly": [155, 333, 389, 333, 389, 381, 155, 381], "score": 0.938}, {"category_id": 0, "poly": [154, 1608, 390, 1608, 390, 1655, 154, 1655], "score": 0.935}, {"category_id": 2, "poly": [459, 2221, 1236, 2221, 1236, 2262, 459, 2262], "score": 0.931}, {"category_id": 0, "poly": [158, 264, 305, 264, 305, 316, 158, 316], "score": 0.931}, {"category_id": 2, "poly": [1366, 2224, 1468, 2224, 1468, 2258, 1366, 2258], "score": 0.923}, {"category_id": 1, "poly": [194, 618, 884, 618, 884, 659, 194, 659], "score": 0.915}, {"category_id": 0, "poly": [154, 669, 311, 669, 311, 714, 154, 714], "score": 0.911}, {"category_id": 1, "poly": [147, 396, 1485, 396, 1485, 492, 147, 492], "score": 0.911}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2259, 187, 2259], "score": 0.911}, {"category_id": 6, "poly": [710, 735, 995, 735, 995, 768, 710, 768], "score": 0.907}, {"category_id": 6, "poly": [748, 1237, 956, 1237, 956, 1269, 748, 1269], "score": 0.9}, {"category_id": 5, "poly": [154, 108, 1494, 108, 1494, 247, 154, 247], "score": 0.899, "html": "<table><tr><td>鹏翎 NG</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 1, "poly": [199, 507, 1135, 507, 1135, 548, 199, 548], "score": 0.848}, {"category_id": 0, "poly": [153, 1168, 388, 1168, 388, 1216, 153, 1216], "score": 0.768}, {"category_id": 1, "poly": [153, 1168, 388, 1168, 388, 1216, 153, 1216], "score": 0.183}, {"category_id": 2, "poly": [155, 110, 349, 110, 349, 247, 155, 247], "score": 0.121}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.28, "latex": "\\circledcirc"}, {"category_id": 15, "poly": [149.0, 1530.0, 613.0, 1530.0, 613.0, 1598.0, 149.0, 1598.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [147.0, 555.0, 391.0, 555.0, 391.0, 606.0, 147.0, 606.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [147.0, 332.0, 393.0, 332.0, 393.0, 385.0, 147.0, 385.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [145.0, 1602.0, 396.0, 1602.0, 396.0, 1660.0, 145.0, 1660.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [493.0, 2219.0, 1237.0, 2219.0, 1237.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [149.0, 258.0, 309.0, 258.0, 309.0, 324.0, 149.0, 324.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1360.0, 2218.0, 1474.0, 2218.0, 1474.0, 2264.0, 1360.0, 2264.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [146.0, 663.0, 316.0, 663.0, 316.0, 720.0, 146.0, 720.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2221.0, 320.0, 2221.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [711.0, 737.0, 993.0, 737.0, 993.0, 766.0, 711.0, 766.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [747.0, 1232.0, 958.0, 1232.0, 958.0, 1272.0, 747.0, 1272.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [147.0, 1165.0, 393.0, 1165.0, 393.0, 1219.0, 147.0, 1219.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 190.0, 284.0, 190.0, 284.0, 232.0, 219.0, 232.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [203.0, 1673.0, 1487.0, 1673.0, 1487.0, 1707.0, 203.0, 1707.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [158.0, 1730.0, 823.0, 1730.0, 823.0, 1763.0, 158.0, 1763.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [199.0, 620.0, 886.0, 620.0, 886.0, 657.0, 199.0, 657.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [200.0, 400.0, 1483.0, 400.0, 1483.0, 433.0, 200.0, 433.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [157.0, 455.0, 1029.0, 455.0, 1029.0, 488.0, 157.0, 488.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [198.0, 509.0, 1134.0, 509.0, 1134.0, 546.0, 198.0, 546.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [147.0, 1165.0, 393.0, 1165.0, 393.0, 1219.0, 147.0, 1219.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 3, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 1, "poly": [151, 954, 1495, 954, 1495, 1157, 151, 1157], "score": 0.976}, {"category_id": 3, "poly": [515, 257, 1185, 257, 1185, 888, 515, 888], "score": 0.964}, {"category_id": 3, "poly": [370, 1160, 1262, 1160, 1262, 2090, 370, 2090], "score": 0.952}, {"category_id": 4, "poly": [693, 905, 1012, 905, 1012, 940, 693, 940], "score": 0.929}, {"category_id": 2, "poly": [459, 2221, 1236, 2221, 1236, 2262, 459, 2262], "score": 0.928}, {"category_id": 2, "poly": [1365, 2224, 1468, 2224, 1468, 2259, 1365, 2259], "score": 0.924}, {"category_id": 4, "poly": [700, 2110, 1003, 2110, 1003, 2143, 700, 2143], "score": 0.915}, {"category_id": 2, "poly": [186, 2223, 318, 2223, 318, 2259, 186, 2259], "score": 0.907}, {"category_id": 2, "poly": [152, 108, 1492, 108, 1492, 246, 152, 246], "score": 0.669}, {"category_id": 2, "poly": [774, 117, 1488, 117, 1488, 231, 774, 231], "score": 0.649}, {"category_id": 2, "poly": [156, 111, 349, 111, 349, 241, 156, 241], "score": 0.574}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.28, "latex": "\\circledcirc"}, {"category_id": 15, "poly": [769.0, 536.0, 784.0, 536.0, 784.0, 543.0, 769.0, 543.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [676.0, 552.0, 684.0, 552.0, 684.0, 563.0, 676.0, 563.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [676.0, 575.0, 688.0, 575.0, 688.0, 582.0, 676.0, 582.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [675.0, 579.0, 689.0, 579.0, 689.0, 591.0, 675.0, 591.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [788.0, 762.0, 799.0, 762.0, 799.0, 769.0, 788.0, 769.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [829.0, 334.5, 850.0, 334.5, 850.0, 346.0, 829.0, 346.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [898.25, 723.5, 909.25, 723.5, 909.25, 734.5, 898.25, 734.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [537.75, 726.0, 683.75, 726.0, 683.75, 818.5, 537.75, 818.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1100.0, 1154.0, 1248.0, 1154.0, 1248.0, 1203.0, 1100.0, 1203.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [472.0, 1254.0, 589.0, 1254.0, 589.0, 1305.0, 472.0, 1305.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1094.0, 1270.0, 1209.0, 1270.0, 1209.0, 1321.0, 1094.0, 1321.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [464.0, 1378.0, 554.0, 1378.0, 554.0, 1419.0, 464.0, 1419.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1116.0, 1444.0, 1263.0, 1444.0, 1263.0, 1496.0, 1116.0, 1496.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [443.0, 1496.0, 559.0, 1496.0, 559.0, 1551.0, 443.0, 1551.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [464.0, 1619.0, 545.0, 1619.0, 545.0, 1673.0, 464.0, 1673.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1062.0, 1653.0, 1081.0, 1653.0, 1081.0, 1669.0, 1062.0, 1669.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1110.0, 1629.0, 1198.0, 1629.0, 1198.0, 1682.0, 1110.0, 1682.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [449.0, 1738.0, 557.0, 1738.0, 557.0, 1791.0, 449.0, 1791.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1065.0, 1774.0, 1085.0, 1774.0, 1085.0, 1791.0, 1065.0, 1791.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1121.0, 1745.0, 1203.0, 1745.0, 1203.0, 1796.0, 1121.0, 1796.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [739.0, 1911.0, 770.0, 1911.0, 770.0, 1964.0, 739.0, 1964.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1046.0, 1945.0, 1065.0, 1945.0, 1065.0, 1961.0, 1046.0, 1961.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1114.0, 1896.0, 1198.0, 1896.0, 1198.0, 1951.0, 1114.0, 1951.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [359.0, 1971.0, 475.0, 1971.0, 475.0, 2026.0, 359.0, 2026.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [543.0, 1998.0, 552.0, 1998.0, 552.0, 2007.0, 543.0, 2007.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [693.0, 906.0, 1009.0, 906.0, 1009.0, 937.0, 693.0, 937.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [493.0, 2219.0, 1237.0, 2219.0, 1237.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1359.0, 2216.0, 1475.0, 2216.0, 1475.0, 2266.0, 1359.0, 2266.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [701.0, 2112.0, 1001.0, 2112.0, 1001.0, 2141.0, 701.0, 2141.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [185.0, 2221.0, 319.0, 2221.0, 319.0, 2267.0, 185.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1336.0, 115.0, 1497.0, 115.0, 1497.0, 170.0, 1336.0, 170.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [217.0, 189.0, 285.0, 189.0, 285.0, 231.0, 217.0, 231.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [777.0, 186.0, 1488.0, 186.0, 1488.0, 228.0, 777.0, 228.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1334.0, 114.0, 1496.0, 114.0, 1496.0, 171.0, 1334.0, 171.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [778.0, 187.0, 1486.0, 187.0, 1486.0, 227.0, 778.0, 227.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 189.0, 284.0, 189.0, 284.0, 230.0, 219.0, 230.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [193.0, 953.0, 1479.0, 953.0, 1479.0, 997.0, 193.0, 997.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [157.0, 1014.0, 1473.0, 1014.0, 1473.0, 1049.0, 157.0, 1049.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [155.0, 1065.0, 1492.0, 1065.0, 1492.0, 1106.0, 155.0, 1106.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [157.0, 1125.0, 391.0, 1125.0, 391.0, 1160.0, 157.0, 1160.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [990.0, 1114.0, 1017.0, 1114.0, 1017.0, 1157.0, 990.0, 1157.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [825.75, 1118.5, 861.75, 1118.5, 861.75, 1152.5, 825.75, 1152.5], "score": 1.0, "text": ""}], "page_info": {"page_no": 4, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [292, 1311, 1360, 1311, 1360, 1641, 292, 1641], "score": 0.981, "html": "<table><tr><td>No.</td><td>Description</td><td>Symbol</td><td>Min.</td><td>Typ.</td><td>Max</td><td>Unit</td><td>Note</td></tr><tr><td>1</td><td>允许电压范围</td><td>UPVR</td><td>-14</td><td></td><td>20</td><td>V</td><td></td></tr><tr><td>2</td><td>正常电压范围</td><td>UNOM</td><td>9</td><td>12</td><td>16</td><td>V</td><td></td></tr><tr><td>3</td><td>供电电流</td><td>INOM</td><td></td><td></td><td>2</td><td>A</td><td></td></tr><tr><td>4</td><td>静态电流</td><td>lauiescent</td><td></td><td></td><td>100</td><td>μA</td><td>@13.5V</td></tr></table>"}, {"category_id": 5, "poly": [198, 542, 1456, 542, 1456, 908, 198, 908], "score": 0.978, "html": "<table><tr><td>PIN 脚</td><td>信号定义</td><td>额定电流 (A)</td><td>额定电压 (V)</td><td>最大电流 (A)</td><td>推荐线径 (mm²)</td><td>其他要求/备注</td></tr><tr><td>1</td><td>GND</td><td>0.5</td><td>0</td><td>1</td><td>0.5</td><td rowspan=\"3\">接插件型号（公端）： 2-1564559-2 Code B</td></tr><tr><td>2</td><td>LIN</td><td>0.02</td><td>12</td><td>0.1</td><td>0.5</td></tr><tr><td>3</td><td>VBAT</td><td>0.5</td><td>12</td><td>1</td><td>0.5</td></tr><tr><td>4</td><td>N.C</td><td></td><td></td><td></td><td></td><td>1 2 3 4</td></tr></table>"}, {"category_id": 5, "poly": [249, 1947, 1406, 1947, 1406, 2066, 249, 2066], "score": 0.976, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-4</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [250, 316, 1408, 316, 1408, 435, 250, 435], "score": 0.976, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-1</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [250, 1708, 1405, 1708, 1405, 1827, 250, 1827], "score": 0.974, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 3</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [250, 974, 1408, 974, 1408, 1093, 250, 1093], "score": 0.97, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 2</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 1, "poly": [152, 1104, 1483, 1104, 1483, 1253, 152, 1253], "score": 0.968}, {"category_id": 2, "poly": [1365, 2224, 1468, 2224, 1468, 2258, 1365, 2258], "score": 0.93}, {"category_id": 2, "poly": [458, 2221, 1236, 2221, 1236, 2262, 458, 2262], "score": 0.928}, {"category_id": 0, "poly": [153, 1899, 402, 1899, 402, 1939, 153, 1939], "score": 0.922}, {"category_id": 1, "poly": [198, 1839, 724, 1839, 724, 1877, 198, 1877], "score": 0.913}, {"category_id": 6, "poly": [718, 1279, 984, 1279, 984, 1309, 718, 1309], "score": 0.908}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2259, 187, 2259], "score": 0.907}, {"category_id": 6, "poly": [705, 508, 999, 508, 999, 539, 705, 539], "score": 0.906}, {"category_id": 1, "poly": [197, 2079, 682, 2079, 682, 2118, 197, 2118], "score": 0.898}, {"category_id": 5, "poly": [154, 108, 1493, 108, 1493, 246, 154, 246], "score": 0.886, "html": "<table><tr><td>鹏翎 NGI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 0, "poly": [153, 1659, 421, 1659, 421, 1699, 153, 1699], "score": 0.846}, {"category_id": 0, "poly": [154, 926, 431, 926, 431, 966, 154, 966], "score": 0.835}, {"category_id": 0, "poly": [154, 269, 414, 269, 414, 314, 154, 314], "score": 0.828}, {"category_id": 1, "poly": [198, 448, 557, 448, 557, 485, 198, 485], "score": 0.589}, {"category_id": 2, "poly": [156, 110, 353, 110, 353, 244, 156, 244], "score": 0.588}, {"category_id": 7, "poly": [198, 448, 557, 448, 557, 485, 198, 485], "score": 0.429}, {"category_id": 13, "poly": [1352, 1108, 1410, 1108, 1410, 1143, 1352, 1143], "score": 0.76, "latex": "U _ { \\mathsf { P V R } }"}, {"category_id": 13, "poly": [1242, 1590, 1344, 1590, 1344, 1623, 1242, 1623], "score": 0.73, "latex": "@ 1 3 . 5 \\lor"}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.44, "latex": "\\circledcirc"}, {"category_id": 13, "poly": [1166, 1597, 1205, 1597, 1205, 1625, 1166, 1625], "score": 0.32, "latex": "\\mu { \\sf A }"}, {"category_id": 15, "poly": [1359.0, 2216.0, 1475.0, 2216.0, 1475.0, 2265.0, 1359.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [493.0, 2219.0, 1237.0, 2219.0, 1237.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 1896.0, 403.0, 1896.0, 403.0, 1942.0, 152.0, 1942.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [715.0, 1276.0, 984.0, 1276.0, 984.0, 1309.0, 715.0, 1309.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2221.0, 320.0, 2221.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [706.0, 508.0, 999.0, 508.0, 999.0, 541.0, 706.0, 541.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 1655.0, 423.0, 1655.0, 423.0, 1703.0, 151.0, 1703.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 923.0, 434.0, 923.0, 434.0, 971.0, 151.0, 971.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [146.0, 262.0, 419.0, 262.0, 419.0, 320.0, 146.0, 320.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 190.0, 283.0, 190.0, 283.0, 231.0, 219.0, 231.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [196.0, 443.0, 559.0, 443.0, 559.0, 490.0, 196.0, 490.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [202.0, 1107.0, 1351.0, 1107.0, 1351.0, 1141.0, 202.0, 1141.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1411.0, 1107.0, 1475.0, 1107.0, 1475.0, 1141.0, 1411.0, 1141.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [153.0, 1157.0, 1476.0, 1157.0, 1476.0, 1202.0, 153.0, 1202.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 1213.0, 211.0, 1213.0, 211.0, 1259.0, 151.0, 1259.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [799.0, 1223.0, 813.0, 1223.0, 813.0, 1245.5, 799.0, 1245.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [907.0, 1203.0, 965.0, 1203.0, 965.0, 1249.0, 907.0, 1249.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 1839.0, 727.0, 1839.0, 727.0, 1880.0, 197.0, 1880.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [195.0, 2077.0, 686.0, 2077.0, 686.0, 2118.0, 195.0, 2118.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [196.0, 443.0, 559.0, 443.0, 559.0, 490.0, 196.0, 490.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 5, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [283, 984, 1371, 984, 1371, 1701, 283, 1701], "score": 0.982, "html": "<table><tr><td>No.</td><td>Description</td><td>Symbol</td><td>Min.</td><td>Typ.</td><td>Max</td><td>Unit</td><td>Note</td></tr><tr><td>1</td><td>电机工作电压</td><td>UNOM</td><td>9</td><td>12</td><td>16</td><td>V</td><td></td></tr><tr><td>2</td><td rowspan=\"2\">电机瞬时电流</td><td>IMAX</td><td></td><td></td><td>800</td><td>mA</td><td></td></tr><tr><td>3</td><td>持续时间</td><td></td><td></td><td>100</td><td>ms</td><td></td></tr><tr><td>4</td><td>电机额定功率</td><td>P</td><td></td><td>2.93</td><td>4.1</td><td>W</td><td></td></tr><tr><td>5</td><td rowspan=\"3\">电机工作电流</td><td>IMAX</td><td></td><td></td><td>0.59</td><td>A</td><td></td></tr><tr><td>6</td><td>额定电流</td><td></td><td>0.318</td><td></td><td>A</td><td></td></tr><tr><td>7</td><td>堵转电流</td><td></td><td></td><td>1.098</td><td>A</td><td></td></tr><tr><td>8</td><td>电机类型</td><td>Mrype</td><td></td><td>BLDC</td><td></td><td></td><td></td></tr><tr><td>9</td><td>开关 Hall工作电压</td><td>VNom</td><td>3.0</td><td>5</td><td>5.5</td><td>V</td><td></td></tr><tr><td>10</td><td>开关 Hall 工作电流</td><td>INom</td><td>1.1</td><td>1.5</td><td>2.5</td><td>mA</td><td></td></tr></table>"}, {"category_id": 5, "poly": [303, 315, 1351, 315, 1351, 582, 303, 582], "score": 0.982, "html": "<table><tr><td>No.</td><td>Description</td><td>Min.</td><td>Typ.</td><td>Max</td><td>Unit</td><td>Note</td></tr><tr><td>1</td><td>波特率 Bandwidth</td><td></td><td>19.2</td><td></td><td>Kbps</td><td></td></tr><tr><td>2</td><td>主从节点</td><td colspan=\"5\">从节点模式</td></tr><tr><td>3</td><td>LIN电流损耗</td><td></td><td></td><td>100</td><td>mA</td><td></td></tr></table>"}, {"category_id": 5, "poly": [249, 703, 1410, 703, 1410, 821, 249, 821], "score": 0.971, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-5</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [249, 1755, 1407, 1755, 1407, 1872, 249, 1872], "score": 0.969, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-6</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 1, "poly": [146, 832, 1479, 832, 1479, 927, 146, 927], "score": 0.948}, {"category_id": 2, "poly": [459, 2221, 1236, 2221, 1236, 2262, 459, 2262], "score": 0.93}, {"category_id": 2, "poly": [1367, 2224, 1467, 2224, 1467, 2258, 1367, 2258], "score": 0.923}, {"category_id": 6, "poly": [722, 950, 983, 950, 983, 981, 722, 981], "score": 0.913}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2258, 187, 2258], "score": 0.909}, {"category_id": 6, "poly": [732, 281, 973, 281, 973, 312, 732, 312], "score": 0.904}, {"category_id": 0, "poly": [154, 586, 429, 586, 429, 634, 154, 634], "score": 0.885}, {"category_id": 5, "poly": [154, 108, 1494, 108, 1494, 247, 154, 247], "score": 0.846, "html": "<table><tr><td>鹏翎 NG</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 0, "poly": [153, 1707, 465, 1707, 465, 1752, 153, 1752], "score": 0.765}, {"category_id": 0, "poly": [153, 654, 382, 654, 382, 695, 153, 695], "score": 0.713}, {"category_id": 2, "poly": [155, 110, 351, 110, 351, 244, 155, 244], "score": 0.71}, {"category_id": 1, "poly": [153, 654, 382, 654, 382, 695, 153, 695], "score": 0.17}, {"category_id": 13, "poly": [716, 1586, 780, 1586, 780, 1618, 716, 1618], "score": 0.37, "latex": "\\mathsf { V } _ { \\mathsf { N o m } }"}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.28, "latex": "\\circledcirc"}, {"category_id": 15, "poly": [493.0, 2219.0, 1237.0, 2219.0, 1237.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1361.0, 2217.0, 1474.0, 2217.0, 1474.0, 2266.0, 1361.0, 2266.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [723.0, 950.0, 982.0, 950.0, 982.0, 983.0, 723.0, 983.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2222.0, 320.0, 2222.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [732.0, 281.0, 973.0, 281.0, 973.0, 314.0, 732.0, 314.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [150.0, 585.0, 429.0, 585.0, 429.0, 636.0, 150.0, 636.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [150.0, 1703.0, 467.0, 1703.0, 467.0, 1756.0, 150.0, 1756.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [149.0, 653.0, 382.0, 653.0, 382.0, 695.0, 149.0, 695.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 191.0, 284.0, 191.0, 284.0, 232.0, 219.0, 232.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [199.0, 829.0, 1484.0, 829.0, 1484.0, 872.0, 199.0, 872.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [156.0, 889.0, 459.0, 889.0, 459.0, 925.0, 156.0, 925.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [938.0, 890.0, 968.0, 890.0, 968.0, 924.0, 938.0, 924.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1128.0, 892.5, 1147.0, 892.5, 1147.0, 914.5, 1128.0, 914.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [149.0, 653.0, 382.0, 653.0, 382.0, 695.0, 149.0, 695.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 6, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [248, 2058, 1405, 2058, 1405, 2176, 248, 2176], "score": 0.974, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-S0R-8</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 3, "poly": [215, 274, 1450, 274, 1450, 1019, 215, 1019], "score": 0.973}, {"category_id": 5, "poly": [250, 1145, 1406, 1145, 1406, 1263, 250, 1263], "score": 0.964, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 7</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 3, "poly": [335, 1330, 1323, 1330, 1323, 1817, 335, 1817], "score": 0.925}, {"category_id": 2, "poly": [1365, 2224, 1467, 2224, 1467, 2258, 1365, 2258], "score": 0.924}, {"category_id": 0, "poly": [154, 1940, 386, 1940, 386, 1993, 154, 1993], "score": 0.912}, {"category_id": 4, "poly": [731, 1852, 972, 1852, 972, 1886, 731, 1886], "score": 0.909}, {"category_id": 1, "poly": [199, 1275, 1215, 1275, 1215, 1315, 199, 1315], "score": 0.908}, {"category_id": 2, "poly": [457, 2221, 1237, 2221, 1237, 2262, 457, 2262], "score": 0.907}, {"category_id": 4, "poly": [707, 1054, 999, 1054, 999, 1088, 707, 1088], "score": 0.901}, {"category_id": 0, "poly": [153, 1097, 387, 1097, 387, 1142, 153, 1142], "score": 0.865}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2259, 187, 2259], "score": 0.798}, {"category_id": 2, "poly": [198, 2221, 1242, 2221, 1242, 2262, 198, 2262], "score": 0.586}, {"category_id": 1, "poly": [154, 2010, 584, 2010, 584, 2055, 154, 2055], "score": 0.548}, {"category_id": 2, "poly": [154, 108, 1490, 108, 1490, 245, 154, 245], "score": 0.502}, {"category_id": 0, "poly": [154, 2010, 584, 2010, 584, 2055, 154, 2055], "score": 0.424}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.43, "latex": "\\circledcirc"}, {"category_id": 15, "poly": [445.0, 306.0, 488.0, 306.0, 488.0, 330.0, 445.0, 330.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [618.0, 305.0, 649.0, 305.0, 649.0, 344.0, 618.0, 344.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [457.0, 324.0, 478.0, 324.0, 478.0, 339.0, 457.0, 339.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [215.0, 334.0, 276.0, 334.0, 276.0, 362.0, 215.0, 362.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [549.0, 332.0, 603.0, 332.0, 603.0, 353.0, 549.0, 353.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [507.0, 382.0, 527.0, 382.0, 527.0, 410.0, 507.0, 410.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [803.0, 375.0, 820.0, 375.0, 820.0, 394.0, 803.0, 394.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [325.0, 402.0, 346.0, 402.0, 346.0, 428.0, 325.0, 428.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 473.0, 272.0, 473.0, 272.0, 501.0, 219.0, 501.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [666.0, 464.0, 731.0, 464.0, 731.0, 498.0, 666.0, 498.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [826.0, 460.0, 851.0, 460.0, 851.0, 505.0, 826.0, 505.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [852.0, 462.0, 876.0, 462.0, 876.0, 506.0, 852.0, 506.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [892.0, 466.0, 915.0, 466.0, 915.0, 494.0, 892.0, 494.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [668.0, 501.0, 792.0, 501.0, 792.0, 523.0, 668.0, 523.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1091.0, 524.0, 1104.0, 524.0, 1104.0, 538.0, 1091.0, 538.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [369.0, 576.0, 429.0, 576.0, 429.0, 598.0, 369.0, 598.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [802.0, 570.0, 1074.0, 570.0, 1074.0, 666.0, 802.0, 666.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [664.0, 598.0, 725.0, 598.0, 725.0, 621.0, 664.0, 621.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [315.0, 620.0, 383.0, 620.0, 383.0, 642.0, 315.0, 642.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [225.0, 631.0, 264.0, 631.0, 264.0, 656.0, 225.0, 656.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [300.0, 637.0, 319.0, 637.0, 319.0, 652.0, 300.0, 652.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [650.0, 633.0, 720.0, 633.0, 720.0, 656.0, 650.0, 656.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [500.0, 652.0, 522.0, 652.0, 522.0, 667.0, 500.0, 667.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [559.0, 669.0, 609.0, 669.0, 609.0, 692.0, 559.0, 692.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [804.0, 657.0, 1073.0, 657.0, 1073.0, 747.0, 804.0, 747.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1091.0, 676.0, 1102.0, 676.0, 1102.0, 688.0, 1091.0, 688.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [574.0, 684.0, 595.0, 684.0, 595.0, 701.0, 574.0, 701.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [444.0, 706.0, 506.0, 706.0, 506.0, 733.0, 444.0, 733.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [547.0, 702.0, 592.0, 702.0, 592.0, 726.0, 547.0, 726.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [664.0, 702.0, 717.0, 702.0, 717.0, 726.0, 664.0, 726.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1093.0, 710.0, 1102.0, 710.0, 1102.0, 722.0, 1093.0, 722.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [468.0, 726.0, 485.0, 726.0, 485.0, 742.0, 468.0, 742.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1091.0, 740.0, 1104.0, 740.0, 1104.0, 754.0, 1091.0, 754.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [664.0, 752.0, 707.0, 752.0, 707.0, 776.0, 664.0, 776.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1261.0, 762.0, 1321.0, 762.0, 1321.0, 780.0, 1261.0, 780.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [664.0, 780.0, 716.0, 780.0, 716.0, 804.0, 664.0, 804.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [404.0, 790.0, 460.0, 790.0, 460.0, 830.0, 404.0, 830.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [666.0, 808.0, 691.0, 808.0, 691.0, 829.0, 666.0, 829.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [564.0, 838.0, 618.0, 838.0, 618.0, 862.0, 564.0, 862.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [649.0, 858.0, 656.0, 858.0, 656.0, 865.0, 649.0, 865.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [664.0, 852.0, 693.0, 852.0, 693.0, 873.0, 664.0, 873.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1272.0, 848.0, 1328.0, 848.0, 1328.0, 866.0, 1272.0, 866.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [407.0, 876.0, 456.0, 876.0, 456.0, 899.0, 407.0, 899.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [563.0, 869.0, 621.0, 869.0, 621.0, 887.0, 563.0, 887.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [645.0, 883.0, 657.0, 883.0, 657.0, 895.0, 645.0, 895.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [664.0, 880.0, 692.0, 880.0, 692.0, 901.0, 664.0, 901.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1076.0, 865.0, 1105.0, 865.0, 1105.0, 887.0, 1076.0, 887.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1247.0, 862.0, 1348.0, 862.0, 1348.0, 902.0, 1247.0, 902.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [554.0, 906.0, 588.0, 906.0, 588.0, 926.0, 554.0, 926.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [666.0, 905.0, 718.0, 905.0, 718.0, 929.0, 666.0, 929.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [378.0, 949.0, 396.0, 949.0, 396.0, 965.0, 378.0, 965.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [595.0, 918.0, 618.0, 918.0, 618.0, 963.0, 595.0, 963.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [602.0, 961.0, 613.0, 961.0, 613.0, 976.0, 602.0, 976.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1273.0, 963.0, 1309.0, 963.0, 1309.0, 983.0, 1273.0, 983.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1163.25, 926.5, 1177.25, 926.5, 1177.25, 941.5, 1163.25, 941.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1014.0, 1334.0, 1108.0, 1334.0, 1108.0, 1368.0, 1014.0, 1368.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1025.0, 1367.0, 1108.0, 1367.0, 1108.0, 1403.0, 1025.0, 1403.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1041.0, 1403.0, 1106.0, 1403.0, 1106.0, 1439.0, 1041.0, 1439.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [522.0, 1592.0, 1135.0, 1592.0, 1135.0, 1645.0, 522.0, 1645.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [683.0, 1646.0, 973.0, 1646.0, 973.0, 1686.0, 683.0, 1686.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1359.0, 2216.0, 1475.0, 2216.0, 1475.0, 2266.0, 1359.0, 2266.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [148.0, 1937.0, 390.0, 1937.0, 390.0, 1996.0, 148.0, 1996.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [731.0, 1854.0, 971.0, 1854.0, 971.0, 1886.0, 731.0, 1886.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [493.0, 2219.0, 1237.0, 2219.0, 1237.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [707.0, 1055.0, 997.0, 1055.0, 997.0, 1086.0, 707.0, 1086.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [148.0, 1095.0, 392.0, 1095.0, 392.0, 1148.0, 148.0, 1148.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2221.0, 320.0, 2221.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [189.0, 2218.0, 321.0, 2218.0, 321.0, 2266.0, 189.0, 2266.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [493.0, 2221.0, 1236.0, 2221.0, 1236.0, 2261.0, 493.0, 2261.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1335.0, 109.0, 1499.0, 109.0, 1499.0, 172.0, 1335.0, 172.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [218.0, 188.0, 284.0, 188.0, 284.0, 230.0, 218.0, 230.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [777.0, 187.0, 1488.0, 187.0, 1488.0, 228.0, 777.0, 228.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 2009.0, 586.0, 2009.0, 586.0, 2061.0, 151.0, 2061.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [196.0, 1274.0, 1216.0, 1274.0, 1216.0, 1315.0, 196.0, 1315.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 2009.0, 586.0, 2009.0, 586.0, 2061.0, 151.0, 2061.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 7, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [248, 1735, 1406, 1735, 1406, 1929, 248, 1929], "score": 0.981, "html": "<table><tr><td>No.</td><td>LIN Msg Name</td><td>ID(Raw)</td><td>Length(Byte)</td><td>Time</td><td>Description</td></tr><tr><td>1</td><td>Valve_Cmd</td><td>0x22</td><td>8</td><td>20ms</td><td rowspan=\"2\">VCU请求水阀运行报文信息</td></tr><tr><td>2</td><td>MasterReq</td><td>0x3C</td><td>8</td><td>Event</td></tr></table>"}, {"category_id": 5, "poly": [238, 1982, 1416, 1982, 1416, 2169, 238, 2169], "score": 0.98, "html": "<table><tr><td>ID</td><td>Signal Name</td><td>Signal Description</td><td>Start Bit</td><td>Bit Length</td><td>Signal Value Description</td></tr><tr><td>0x22</td><td>ECC_TWV_PosSet</td><td>水阀目标位置请求</td><td>24</td><td>8</td><td>PH=INT*0.4(%) nvalid:0xFB~OxFF</td></tr></table>"}, {"category_id": 5, "poly": [249, 539, 1407, 539, 1407, 831, 249, 831], "score": 0.971, "html": "<table><tr><td>No.</td><td>电源电压模式</td><td>低电压区间</td><td>高电压区间</td></tr><tr><td>1</td><td rowspan=\"2\">电压A模式</td><td>Enter: ≥9.0V，&gt;500ms</td><td>Enter: ≤16.0V，&gt;500ms</td></tr><tr><td>2</td><td>Exit: ≤8.5V，&gt;500ms</td><td>Exit：≥16.5V，&gt;500ms</td></tr><tr><td>3</td><td rowspan=\"2\">电压B模式</td><td>Enter：≤8.5V, &gt;500ms</td><td>Enter:≥16.5V, &gt;500ms</td></tr><tr><td>4</td><td>Exit：≥9.0V, &gt;500ms</td><td>Exit：≤16.0V，&gt;500ms</td></tr></table>"}, {"category_id": 5, "poly": [250, 1508, 1406, 1508, 1406, 1627, 250, 1627], "score": 0.968, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 10</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [249, 1045, 1407, 1045, 1407, 1165, 249, 1165], "score": 0.967, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-9</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 1, "poly": [198, 385, 1454, 385, 1454, 482, 198, 482], "score": 0.961}, {"category_id": 1, "poly": [161, 274, 1478, 274, 1478, 370, 161, 370], "score": 0.945}, {"category_id": 2, "poly": [1365, 2224, 1468, 2224, 1468, 2259, 1365, 2259], "score": 0.928}, {"category_id": 2, "poly": [459, 2221, 1236, 2221, 1236, 2262, 459, 2262], "score": 0.92}, {"category_id": 1, "poly": [198, 1638, 890, 1638, 890, 1679, 198, 1679], "score": 0.917}, {"category_id": 1, "poly": [199, 1176, 884, 1176, 884, 1217, 199, 1217], "score": 0.916}, {"category_id": 6, "poly": [635, 503, 1068, 503, 1068, 535, 635, 535], "score": 0.914}, {"category_id": 6, "poly": [724, 1700, 980, 1700, 980, 1731, 724, 1731], "score": 0.912}, {"category_id": 6, "poly": [700, 1948, 1004, 1948, 1004, 1978, 700, 1978], "score": 0.912}, {"category_id": 0, "poly": [154, 1460, 448, 1460, 448, 1500, 154, 1500], "score": 0.893}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2259, 187, 2259], "score": 0.887}, {"category_id": 1, "poly": [197, 1233, 1387, 1233, 1387, 1440, 197, 1440], "score": 0.837}, {"category_id": 1, "poly": [206, 834, 275, 834, 275, 865, 206, 865], "score": 0.785}, {"category_id": 1, "poly": [268, 836, 926, 836, 926, 991, 268, 991], "score": 0.774}, {"category_id": 0, "poly": [153, 1001, 372, 1001, 372, 1043, 153, 1043], "score": 0.741}, {"category_id": 2, "poly": [155, 108, 1494, 108, 1494, 247, 155, 247], "score": 0.306}, {"category_id": 1, "poly": [153, 1001, 372, 1001, 372, 1043, 153, 1043], "score": 0.178}, {"category_id": 7, "poly": [268, 836, 926, 836, 926, 991, 268, 991], "score": 0.136}, {"category_id": 13, "poly": [660, 961, 775, 961, 775, 988, 660, 988], "score": 0.86, "latex": "\\_"}, {"category_id": 13, "poly": [570, 878, 628, 878, 628, 905, 570, 905], "score": 0.84, "latex": "\\cdot"}, {"category_id": 13, "poly": [694, 609, 786, 609, 786, 645, 694, 645], "score": 0.73, "latex": "{ \\geqslant } 9 . 0 \\nu"}, {"category_id": 13, "poly": [1099, 667, 1207, 667, 1207, 703, 1099, 703], "score": 0.71, "latex": "\\geqslant 1 6 . 5 \\lor"}, {"category_id": 13, "poly": [1110, 724, 1218, 724, 1218, 760, 1110, 760], "score": 0.67, "latex": "\\geqslant 1 6 . 5 \\lor"}, {"category_id": 13, "poly": [694, 725, 786, 725, 786, 760, 694, 760], "score": 0.56, "latex": "\\leqslant 8 . 5 \\mathsf { V }"}, {"category_id": 13, "poly": [1110, 609, 1218, 609, 1218, 645, 1110, 645], "score": 0.53, "latex": "{ \\leqslant } 1 6 . 0 \\lor"}, {"category_id": 13, "poly": [684, 668, 776, 668, 776, 702, 684, 702], "score": 0.46, "latex": "\\leqslant 8 . 5 \\lor"}, {"category_id": 13, "poly": [1162, 2081, 1351, 2081, 1351, 2114, 1162, 2114], "score": 0.45, "latex": "\\mathsf { P H } { = } 1 \\mathsf { N T } ^ { * } 0 . 4 ( \\% )"}, {"category_id": 13, "poly": [810, 610, 930, 610, 930, 645, 810, 645], "score": 0.45, "latex": "> 5 0 0 \\mathrm { m s }"}, {"category_id": 13, "poly": [683, 782, 776, 782, 776, 818, 683, 818], "score": 0.43, "latex": "{ \\geqslant } 9 . 0 \\nu"}, {"category_id": 13, "poly": [1230, 668, 1350, 668, 1350, 706, 1230, 706], "score": 0.43, "latex": "> 5 0 0 \\mathsf { m s }"}, {"category_id": 13, "poly": [272, 920, 294, 920, 294, 944, 272, 944], "score": 0.38, "latex": "\\checkmark"}, {"category_id": 13, "poly": [1241, 610, 1360, 610, 1360, 645, 1241, 645], "score": 0.36, "latex": "> 5 0 0 \\mathrm { m s }"}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.35, "latex": "\\circledcirc"}, {"category_id": 13, "poly": [196, 445, 225, 445, 225, 477, 196, 477], "score": 0.35, "latex": "\\gtrdot"}, {"category_id": 13, "poly": [196, 1402, 225, 1402, 225, 1435, 196, 1435], "score": 0.32, "latex": "\\gtrdot"}, {"category_id": 13, "poly": [272, 961, 294, 961, 294, 985, 272, 985], "score": 0.29, "latex": "\\cdot"}, {"category_id": 13, "poly": [1241, 724, 1362, 724, 1362, 762, 1241, 762], "score": 0.28, "latex": "> 5 0 0 \\mathsf { m s }"}, {"category_id": 13, "poly": [499, 961, 565, 961, 565, 988, 499, 988], "score": 0.25, "latex": "5 0 0 \\mathsf { m s }"}, {"category_id": 15, "poly": [1359.0, 2216.0, 1475.0, 2216.0, 1475.0, 2266.0, 1359.0, 2266.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [493.0, 2219.0, 1237.0, 2219.0, 1237.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [636.0, 505.0, 1068.0, 505.0, 1068.0, 537.0, 636.0, 537.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [724.0, 1699.0, 980.0, 1699.0, 980.0, 1732.0, 724.0, 1732.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [698.0, 1947.0, 1005.0, 1947.0, 1005.0, 1980.0, 698.0, 1980.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 1458.0, 449.0, 1458.0, 449.0, 1503.0, 152.0, 1503.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2221.0, 320.0, 2221.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 996.0, 373.0, 996.0, 373.0, 1050.0, 151.0, 1050.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1337.0, 115.0, 1498.0, 115.0, 1498.0, 170.0, 1337.0, 170.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [217.0, 188.0, 285.0, 188.0, 285.0, 231.0, 217.0, 231.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [778.0, 186.0, 1489.0, 186.0, 1489.0, 228.0, 778.0, 228.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [273.0, 876.0, 569.0, 876.0, 569.0, 907.0, 273.0, 907.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [629.0, 876.0, 639.0, 876.0, 639.0, 907.0, 629.0, 907.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [295.0, 918.0, 753.0, 918.0, 753.0, 948.0, 295.0, 948.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [295.0, 958.0, 498.0, 958.0, 498.0, 989.0, 295.0, 989.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [566.0, 958.0, 659.0, 958.0, 659.0, 989.0, 566.0, 989.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [776.0, 958.0, 924.0, 958.0, 924.0, 989.0, 776.0, 989.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [199.0, 390.0, 1443.0, 390.0, 1443.0, 424.0, 199.0, 424.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [226.0, 444.0, 1319.0, 444.0, 1319.0, 478.0, 226.0, 478.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [200.0, 278.0, 1473.0, 278.0, 1473.0, 311.0, 200.0, 311.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [158.0, 334.0, 539.0, 334.0, 539.0, 368.0, 158.0, 368.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [199.0, 1638.0, 889.0, 1638.0, 889.0, 1679.0, 199.0, 1679.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 1175.0, 888.0, 1175.0, 888.0, 1218.0, 197.0, 1218.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [198.0, 1235.0, 568.0, 1235.0, 568.0, 1269.0, 198.0, 1269.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [196.0, 1286.0, 687.0, 1286.0, 687.0, 1328.0, 196.0, 1328.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 1342.0, 646.0, 1342.0, 646.0, 1382.0, 197.0, 1382.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [226.0, 1393.0, 1383.0, 1393.0, 1383.0, 1443.0, 226.0, 1443.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [201.0, 828.0, 281.0, 828.0, 281.0, 872.0, 201.0, 872.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [273.0, 876.0, 569.0, 876.0, 569.0, 907.0, 273.0, 907.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [629.0, 876.0, 639.0, 876.0, 639.0, 907.0, 629.0, 907.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [295.0, 918.0, 753.0, 918.0, 753.0, 948.0, 295.0, 948.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [295.0, 958.0, 498.0, 958.0, 498.0, 989.0, 295.0, 989.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [566.0, 958.0, 659.0, 958.0, 659.0, 989.0, 566.0, 989.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [776.0, 958.0, 924.0, 958.0, 924.0, 989.0, 776.0, 989.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 996.0, 373.0, 996.0, 373.0, 1050.0, 151.0, 1050.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 8, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [213, 958, 1420, 958, 1420, 1965, 213, 1965], "score": 0.985, "html": "<table><tr><td>ID</td><td>Signal Name</td><td>Signal Description</td><td>Start Bit</td><td>Bit Length</td><td> Signal Value Description</td></tr><tr><td>0x24</td><td>TWV_Resp_Error</td><td>LIN通信错误</td><td>0</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_OverVoltage Flt</td><td>水阀过压故障</td><td></td><td></td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_OverCurrent</td><td>水阀过流故障</td><td>2</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_OverTempFI t</td><td>水阀过温故障</td><td>3</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_UnderVolta geFlt</td><td>水阀欠压故障</td><td>4</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_Initsta</td><td>水阀始标</td><td>5</td><td>1</td><td>Ox1:Have nilized</td></tr><tr><td>TWV_StallFIlt</td><td>水阀堵转故障</td><td>6</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TwwMotorsta</td><td>水阀电机状态</td><td>7</td><td>1</td><td>0x0:Motor Running 0x1:Motor Stop</td></tr><tr><td>TWV_RealSta</td><td>水阀位置反馈</td><td>8</td><td>8</td><td>PH=INT*0.4(%) Invalid:0xFB~0xFF</td></tr><tr><td>0x3D</td><td>SlaveRespMsg</td><td>诊断反馈帧</td><td>0</td><td>64</td><td></td></tr></table>"}, {"category_id": 5, "poly": [249, 711, 1407, 711, 1407, 906, 249, 906], "score": 0.979, "html": "<table><tr><td>No.</td><td>LIN Msg Name</td><td>ID(Raw)</td><td>Length(Byte)</td><td>Time</td><td>Description</td></tr><tr><td>1</td><td>Valve_Rsp</td><td>0x24</td><td>8</td><td>20ms</td><td>EWV反馈水阀运行报文信息</td></tr><tr><td>2</td><td>SlaveResp</td><td>0x3D</td><td>8</td><td>Event</td><td></td></tr></table>"}, {"category_id": 5, "poly": [249, 485, 1407, 485, 1407, 603, 249, 603], "score": 0.976, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 11</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [238, 260, 1416, 260, 1416, 419, 238, 419], "score": 0.974, "html": "<table><tr><td></td><td></td><td>水阀使能信号</td><td>32</td><td>1</td><td></td></tr><tr><td>0x3C</td><td>MasterReqMsg</td><td>诊断请求帧</td><td>0</td><td>64</td><td></td></tr></table>"}, {"category_id": 5, "poly": [249, 2034, 1407, 2034, 1407, 2151, 249, 2151], "score": 0.968, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 12</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 2, "poly": [1368, 2224, 1471, 2224, 1471, 2258, 1368, 2258], "score": 0.93}, {"category_id": 2, "poly": [458, 2221, 1235, 2221, 1235, 2262, 458, 2262], "score": 0.927}, {"category_id": 6, "poly": [724, 677, 981, 677, 981, 707, 724, 707], "score": 0.919}, {"category_id": 6, "poly": [699, 924, 1009, 924, 1009, 955, 699, 955], "score": 0.914}, {"category_id": 1, "poly": [198, 615, 889, 615, 889, 655, 198, 655], "score": 0.909}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2259, 187, 2259], "score": 0.891}, {"category_id": 0, "poly": [154, 436, 455, 436, 455, 477, 154, 477], "score": 0.881}, {"category_id": 0, "poly": [153, 1984, 387, 1984, 387, 2025, 153, 2025], "score": 0.873}, {"category_id": 5, "poly": [154, 108, 1492, 108, 1492, 244, 154, 244], "score": 0.83, "html": "<table><tr><td>鹏翎</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.44, "latex": "\\circledcirc"}, {"category_id": 15, "poly": [1361.0, 2216.0, 1480.0, 2216.0, 1480.0, 2265.0, 1361.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [493.0, 2219.0, 1235.0, 2219.0, 1235.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [723.0, 676.0, 981.0, 676.0, 981.0, 711.0, 723.0, 711.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [697.0, 919.0, 1013.0, 919.0, 1013.0, 960.0, 697.0, 960.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2221.0, 320.0, 2221.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 435.0, 454.0, 435.0, 454.0, 478.0, 152.0, 478.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 1982.0, 389.0, 1982.0, 389.0, 2028.0, 151.0, 2028.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [199.0, 614.0, 889.0, 614.0, 889.0, 656.0, 199.0, 656.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 9, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [386, 1136, 1268, 1136, 1268, 1657, 386, 1657], "score": 0.981, "html": "<table><tr><td>序号</td><td>符号</td><td>定义参数</td><td>备注</td></tr><tr><td>1</td><td>NAD</td><td>0x32</td><td></td></tr><tr><td>2</td><td>PCI</td><td>0x06</td><td></td></tr><tr><td>3</td><td>SID</td><td>0xB2/0xB4</td><td></td></tr><tr><td>4</td><td>RSID</td><td>0xF2/0xF4</td><td></td></tr><tr><td>5</td><td>Supplier_ID</td><td>0x0000</td><td>暂定0000</td></tr><tr><td>6</td><td>Function_ID</td><td>0x0000</td><td>暂定0000</td></tr></table>"}, {"category_id": 5, "poly": [179, 603, 1476, 603, 1476, 823, 179, 823], "score": 0.981, "html": "<table><tr><td>ID</td><td>Master ReqBO</td><td>Master ReqB1</td><td>Master ReqB2</td><td>Master ReqB3</td><td>Master ReqB4</td><td>Master ReqB5</td><td>Master ReqB6</td><td>Master ReqB7</td></tr><tr><td>0x3C</td><td>NAD</td><td>PCI</td><td>SID</td><td>子服务</td><td>Suppier</td><td>Suplierr</td><td>Functinn</td><td>Functionn</td></tr></table>"}, {"category_id": 5, "poly": [271, 875, 1383, 875, 1383, 1047, 271, 1047], "score": 0.98, "html": "<table><tr><td>ID</td><td>Slave ResB0</td><td>Slave ResB1</td><td>Slave ResB2</td><td>Slave ResB3</td><td>Slave ResB4</td><td>Slave ResB5</td><td>Slave ResB6</td><td>Slave ResB7</td></tr><tr><td>0x3D</td><td>NAD</td><td>PCI</td><td>RSID注1</td><td>DO</td><td>D1</td><td>D2 -</td><td>D3</td><td>D4</td></tr></table>"}, {"category_id": 5, "poly": [207, 1999, 1449, 1999, 1449, 2153, 207, 2153], "score": 0.979, "html": "<table><tr><td>ID</td><td>Master ReqBO</td><td>Master ReqB1</td><td>Master ReqB2</td><td>Master ReqB3</td><td>Master ReqB4</td><td>Master ReqB5</td><td>Master ReqB6</td><td>Master ReqB7</td></tr><tr><td>0x3C</td><td>0x32</td><td>0x06</td><td>0xB4</td><td>0x00</td><td>0x00</td><td>0x00</td><td>0x00</td><td>0x00</td></tr></table>"}, {"category_id": 5, "poly": [249, 433, 1407, 433, 1407, 552, 249, 552], "score": 0.977, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 13</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [250, 1717, 1406, 1717, 1406, 1835, 250, 1835], "score": 0.975, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 14</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 1, "poly": [159, 1847, 1462, 1847, 1462, 1940, 159, 1940], "score": 0.959}, {"category_id": 1, "poly": [149, 274, 1445, 274, 1445, 368, 149, 368], "score": 0.943}, {"category_id": 2, "poly": [1368, 2224, 1471, 2224, 1471, 2259, 1368, 2259], "score": 0.929}, {"category_id": 2, "poly": [459, 2221, 1236, 2221, 1236, 2262, 459, 2262], "score": 0.927}, {"category_id": 6, "poly": [675, 841, 1028, 841, 1028, 872, 675, 872], "score": 0.915}, {"category_id": 6, "poly": [701, 1965, 1002, 1965, 1002, 1996, 701, 1996], "score": 0.912}, {"category_id": 6, "poly": [748, 1102, 957, 1102, 957, 1132, 748, 1132], "score": 0.91}, {"category_id": 6, "poly": [677, 569, 1025, 569, 1025, 599, 677, 599], "score": 0.908}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2259, 187, 2259], "score": 0.897}, {"category_id": 7, "poly": [203, 1049, 459, 1049, 459, 1081, 203, 1081], "score": 0.843}, {"category_id": 0, "poly": [154, 389, 515, 389, 515, 428, 154, 428], "score": 0.83}, {"category_id": 0, "poly": [154, 1674, 557, 1674, 557, 1712, 154, 1712], "score": 0.644}, {"category_id": 5, "poly": [154, 108, 1491, 108, 1491, 247, 154, 247], "score": 0.627, "html": "<table><tr><td>鹏翎 INGI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 2, "poly": [156, 110, 350, 110, 350, 245, 156, 245], "score": 0.322}, {"category_id": 1, "poly": [154, 1674, 557, 1674, 557, 1712, 154, 1712], "score": 0.203}, {"category_id": 2, "poly": [776, 188, 1488, 188, 1488, 231, 776, 231], "score": 0.17}, {"category_id": 13, "poly": [280, 1050, 457, 1050, 457, 1079, 280, 1079], "score": 0.72, "latex": "R S 1 D = S 1 D + 0 \\times 4 0"}, {"category_id": 13, "poly": [735, 1850, 803, 1850, 803, 1884, 735, 1884], "score": 0.67, "latex": "0 \\times 8 4"}, {"category_id": 13, "poly": [449, 1849, 605, 1849, 605, 1886, 449, 1886], "score": 0.48, "latex": "0 \\times 3 0 < / 0 \\times 3 0"}, {"category_id": 13, "poly": [454, 277, 612, 277, 612, 314, 454, 314], "score": 0.34, "latex": "0 \\times 3 0 < / 0 \\times 3 0"}, {"category_id": 13, "poly": [456, 277, 524, 277, 524, 312, 456, 312], "score": 0.32, "latex": "0 \\times 3 0"}, {"category_id": 13, "poly": [458, 2223, 491, 2223, 491, 2258, 458, 2258], "score": 0.31, "latex": "\\circledcirc"}, {"category_id": 13, "poly": [449, 1849, 517, 1849, 517, 1885, 449, 1885], "score": 0.28, "latex": "0 \\times 3 0"}, {"category_id": 15, "poly": [1362.0, 2218.0, 1479.0, 2218.0, 1479.0, 2264.0, 1362.0, 2264.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [492.0, 2219.0, 1234.0, 2219.0, 1234.0, 2265.0, 492.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [675.0, 841.0, 1029.0, 841.0, 1029.0, 874.0, 675.0, 874.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [701.0, 1965.0, 1002.0, 1965.0, 1002.0, 1996.0, 701.0, 1996.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [748.0, 1102.0, 957.0, 1102.0, 957.0, 1134.0, 748.0, 1134.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [677.0, 570.0, 1026.0, 570.0, 1026.0, 601.0, 677.0, 601.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2221.0, 320.0, 2221.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [203.0, 1045.0, 279.0, 1045.0, 279.0, 1085.0, 203.0, 1085.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 390.0, 516.0, 390.0, 516.0, 431.0, 152.0, 431.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [153.0, 1674.0, 555.0, 1674.0, 555.0, 1715.0, 153.0, 1715.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 190.0, 284.0, 190.0, 284.0, 231.0, 219.0, 231.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [779.0, 186.0, 1486.0, 186.0, 1486.0, 228.0, 779.0, 228.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [201.0, 1850.0, 448.0, 1850.0, 448.0, 1883.0, 201.0, 1883.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [606.0, 1850.0, 734.0, 1850.0, 734.0, 1883.0, 606.0, 1883.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [804.0, 1850.0, 1464.0, 1850.0, 1464.0, 1883.0, 804.0, 1883.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [154.0, 1898.0, 208.0, 1898.0, 208.0, 1948.0, 154.0, 1948.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [198.0, 276.0, 453.0, 276.0, 453.0, 313.0, 198.0, 313.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [613.0, 276.0, 1440.0, 276.0, 1440.0, 313.0, 613.0, 313.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [150.0, 324.0, 212.0, 324.0, 212.0, 377.0, 150.0, 377.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [153.0, 1674.0, 555.0, 1674.0, 555.0, 1715.0, 153.0, 1715.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 10, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [224, 316, 1428, 316, 1428, 526, 224, 526], "score": 0.98, "html": "<table><tr><td>ID</td><td>Slave ResBO</td><td>Slave ResB1</td><td>Slave ResB2</td><td>Slave ResB3</td><td>Slave ResB4</td><td>Slave ResB5</td><td>Slave ResB6</td><td>Slave ResB7</td></tr><tr><td>0x3D</td><td>0x32</td><td>0x06</td><td>0xF4</td><td>软件 主版本</td><td>软件 次版本</td><td>硬件 主版本</td><td>硬件 次版本</td><td>OxFF</td></tr></table>"}, {"category_id": 5, "poly": [241, 1463, 1412, 1463, 1412, 1607, 241, 1607], "score": 0.977, "html": "<table><tr><td>ID</td><td>Slave ResBO</td><td>Slave ResB1</td><td>Slave ResB2</td><td>Slave ResB3</td><td>Slave ResB4</td><td>Slave ResB5</td><td>Slave ResB6</td><td>Slave ResB7</td></tr><tr><td>0x3D</td><td>0x32</td><td>0×06</td><td>0xF2</td><td>0x01注2</td><td>0x00</td><td>0x00</td><td>0x00</td><td>OxFF</td></tr></table>"}, {"category_id": 5, "poly": [210, 1255, 1446, 1255, 1446, 1409, 210, 1409], "score": 0.976, "html": "<table><tr><td>ID</td><td>Master ReqBO</td><td>Master ReqB1</td><td>Master ReqB2</td><td>Master ReqB3</td><td>Master ReqB4</td><td>Master ReqB5</td><td>Master ReqB6</td><td>Master ReqB7</td></tr><tr><td>0x3C</td><td>0x32</td><td>0x06</td><td>0xB2</td><td>0x30</td><td>0x00</td><td>0x00</td><td>0x00</td><td>0x00</td></tr></table>"}, {"category_id": 5, "poly": [248, 1884, 1408, 1884, 1408, 2001, 248, 2001], "score": 0.973, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 16</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 1, "poly": [156, 2014, 1476, 2014, 1476, 2110, 156, 2110], "score": 0.973}, {"category_id": 5, "poly": [251, 972, 1410, 972, 1410, 1091, 251, 1091], "score": 0.969, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 15</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 1, "poly": [155, 1103, 1488, 1103, 1488, 1197, 155, 1197], "score": 0.961}, {"category_id": 2, "poly": [1368, 2224, 1472, 2224, 1472, 2259, 1368, 2259], "score": 0.929}, {"category_id": 2, "poly": [458, 2221, 1235, 2221, 1235, 2262, 458, 2262], "score": 0.926}, {"category_id": 6, "poly": [695, 1220, 1008, 1220, 1008, 1251, 695, 1251], "score": 0.91}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2258, 187, 2258], "score": 0.909}, {"category_id": 6, "poly": [693, 1429, 1010, 1429, 1010, 1459, 693, 1459], "score": 0.902}, {"category_id": 6, "poly": [699, 281, 1005, 281, 1005, 312, 699, 312], "score": 0.901}, {"category_id": 0, "poly": [154, 1837, 349, 1837, 349, 1881, 154, 1881], "score": 0.897}, {"category_id": 0, "poly": [154, 930, 486, 930, 486, 967, 154, 967], "score": 0.804}, {"category_id": 1, "poly": [197, 1623, 270, 1623, 270, 1659, 197, 1659], "score": 0.786}, {"category_id": 1, "poly": [193, 593, 1477, 593, 1477, 686, 193, 686], "score": 0.783}, {"category_id": 1, "poly": [220, 704, 688, 704, 688, 744, 220, 744], "score": 0.779}, {"category_id": 1, "poly": [197, 537, 399, 537, 399, 576, 197, 576], "score": 0.77}, {"category_id": 5, "poly": [153, 108, 1495, 108, 1495, 247, 153, 247], "score": 0.724, "html": "<table><tr><td>鹏翎 LING</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 1, "poly": [204, 871, 691, 871, 691, 910, 204, 910], "score": 0.686}, {"category_id": 1, "poly": [192, 759, 1480, 759, 1480, 852, 192, 852], "score": 0.426}, {"category_id": 1, "poly": [199, 1676, 795, 1676, 795, 1773, 199, 1773], "score": 0.253}, {"category_id": 13, "poly": [1087, 763, 1167, 763, 1167, 797, 1087, 797], "score": 0.84, "latex": "\\mathsf { A } \\mathsf { - } \\mathsf { 4 1 }"}, {"category_id": 13, "poly": [1087, 596, 1167, 596, 1167, 630, 1087, 630], "score": 0.83, "latex": "\\mathsf { A } \\mathsf { - } \\mathsf { 4 1 }"}, {"category_id": 13, "poly": [776, 1559, 879, 1559, 879, 1596, 776, 1596], "score": 0.82, "latex": "0 \\times 0 1 ^ { \\sharp _ { 2 } }"}, {"category_id": 13, "poly": [386, 1735, 439, 1735, 439, 1769, 386, 1769], "score": 0.79, "latex": "0 { \\times } 0"}, {"category_id": 13, "poly": [1192, 596, 1270, 596, 1270, 630, 1192, 630], "score": 0.74, "latex": "\\tt B \\mathrm { \\cdot } > 4 2"}, {"category_id": 13, "poly": [1293, 595, 1374, 595, 1374, 630, 1293, 630], "score": 0.74, "latex": "c \\mathord { \\mathrm { \\sim } } 4 3"}, {"category_id": 13, "poly": [449, 1105, 605, 1105, 605, 1142, 449, 1142], "score": 0.71, "latex": "0 \\times 3 0 < / 0 \\times 3 0"}, {"category_id": 13, "poly": [1191, 763, 1270, 763, 1270, 797, 1191, 797], "score": 0.62, "latex": "\\mathsf { B } \\mathrm { - } \\mathsf { 4 } 2"}, {"category_id": 13, "poly": [987, 1106, 1055, 1106, 1055, 1140, 987, 1140], "score": 0.6, "latex": "\\mathtt { 0 } \\mathtt { x } \\mathtt { 3 0 }"}, {"category_id": 13, "poly": [1049, 1563, 1115, 1563, 1115, 1596, 1049, 1596], "score": 0.53, "latex": "_ { 0 \\times 0 0 }"}, {"category_id": 13, "poly": [921, 1563, 987, 1563, 987, 1596, 921, 1596], "score": 0.5, "latex": "_ { 0 \\times 0 0 }"}, {"category_id": 13, "poly": [386, 1679, 437, 1679, 437, 1713, 386, 1713], "score": 0.47, "latex": "0 \\times 1"}, {"category_id": 13, "poly": [1200, 1360, 1266, 1360, 1266, 1394, 1200, 1394], "score": 0.43, "latex": "_ { 0 \\times 0 0 }"}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.42, "latex": "\\circledcirc"}, {"category_id": 13, "poly": [191, 763, 220, 763, 220, 795, 191, 795], "score": 0.39, "latex": "\\gtrdot"}, {"category_id": 13, "poly": [1054, 1360, 1120, 1360, 1120, 1393, 1054, 1393], "score": 0.34, "latex": "_ { 0 \\times 0 0 }"}, {"category_id": 13, "poly": [196, 1678, 225, 1678, 225, 1712, 196, 1712], "score": 0.33, "latex": "\\gtrdot"}, {"category_id": 13, "poly": [528, 1559, 611, 1559, 611, 1597, 528, 1597], "score": 0.31, "latex": "0 \\times 0 6"}, {"category_id": 13, "poly": [1187, 1563, 1253, 1563, 1253, 1597, 1187, 1597], "score": 0.31, "latex": "_ { 0 \\times 0 0 }"}, {"category_id": 13, "poly": [196, 1734, 225, 1734, 225, 1768, 196, 1768], "score": 0.28, "latex": "\\gtrdot"}, {"category_id": 15, "poly": [1363.0, 2218.0, 1478.0, 2218.0, 1478.0, 2264.0, 1363.0, 2264.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [493.0, 2219.0, 1235.0, 2219.0, 1235.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [697.0, 1218.0, 1008.0, 1218.0, 1008.0, 1251.0, 697.0, 1251.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2222.0, 320.0, 2222.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [693.0, 1428.0, 1011.0, 1428.0, 1011.0, 1460.0, 693.0, 1460.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [700.0, 281.0, 1004.0, 281.0, 1004.0, 314.0, 700.0, 314.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [149.0, 1833.0, 351.0, 1833.0, 351.0, 1888.0, 149.0, 1888.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [154.0, 929.0, 485.0, 929.0, 485.0, 970.0, 154.0, 970.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [202.0, 2018.0, 1476.0, 2018.0, 1476.0, 2051.0, 202.0, 2051.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [153.0, 2068.0, 1366.0, 2068.0, 1366.0, 2108.0, 153.0, 2108.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [201.0, 1103.0, 448.0, 1103.0, 448.0, 1141.0, 201.0, 1141.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [606.0, 1103.0, 986.0, 1103.0, 986.0, 1141.0, 606.0, 1141.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1056.0, 1103.0, 1490.0, 1103.0, 1490.0, 1141.0, 1056.0, 1141.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [156.0, 1159.0, 448.0, 1159.0, 448.0, 1197.0, 156.0, 1197.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [771.0, 1168.0, 813.0, 1168.0, 813.0, 1202.0, 771.0, 1202.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [984.0, 1156.0, 1045.0, 1156.0, 1045.0, 1178.0, 984.0, 1178.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [191.0, 1612.0, 280.0, 1612.0, 280.0, 1664.0, 191.0, 1664.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [193.0, 594.0, 1086.0, 594.0, 1086.0, 632.0, 193.0, 632.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1168.0, 594.0, 1191.0, 594.0, 1191.0, 632.0, 1168.0, 632.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1271.0, 594.0, 1292.0, 594.0, 1292.0, 632.0, 1271.0, 632.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1375.0, 594.0, 1472.0, 594.0, 1472.0, 632.0, 1375.0, 632.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [188.0, 641.0, 250.0, 641.0, 250.0, 694.0, 188.0, 694.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1305.0, 628.5, 1335.0, 628.5, 1335.0, 694.5, 1305.0, 694.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [229.0, 705.0, 686.0, 705.0, 686.0, 744.0, 229.0, 744.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 536.0, 401.0, 536.0, 401.0, 577.0, 197.0, 577.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 871.0, 691.0, 871.0, 691.0, 912.0, 197.0, 912.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [221.0, 757.0, 1086.0, 757.0, 1086.0, 799.0, 221.0, 799.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1168.0, 757.0, 1190.0, 757.0, 1190.0, 799.0, 1168.0, 799.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1271.0, 757.0, 1474.0, 757.0, 1474.0, 799.0, 1271.0, 799.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [190.0, 812.0, 248.0, 812.0, 248.0, 858.0, 190.0, 858.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [226.0, 1674.0, 385.0, 1674.0, 385.0, 1714.0, 226.0, 1714.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [438.0, 1674.0, 794.0, 1674.0, 794.0, 1714.0, 438.0, 1714.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [226.0, 1734.0, 385.0, 1734.0, 385.0, 1770.0, 226.0, 1770.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [440.0, 1734.0, 796.0, 1734.0, 796.0, 1770.0, 440.0, 1770.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 11, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [249, 840, 1405, 840, 1405, 958, 249, 958], "score": 0.975, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 18</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [249, 323, 1408, 323, 1408, 441, 249, 441], "score": 0.971, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 17</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 1, "poly": [214, 1249, 1486, 1249, 1486, 1345, 214, 1345], "score": 0.965}, {"category_id": 1, "poly": [214, 1358, 1484, 1358, 1484, 1508, 214, 1508], "score": 0.963}, {"category_id": 2, "poly": [459, 2221, 1235, 2221, 1235, 2262, 459, 2262], "score": 0.93}, {"category_id": 2, "poly": [1368, 2224, 1471, 2224, 1471, 2258, 1368, 2258], "score": 0.92}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2258, 187, 2258], "score": 0.908}, {"category_id": 1, "poly": [198, 501, 999, 501, 999, 773, 198, 773], "score": 0.907}, {"category_id": 5, "poly": [154, 108, 1493, 108, 1493, 247, 154, 247], "score": 0.905, "html": "<table><tr><td>鹏翎 NE</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 1, "poly": [197, 971, 452, 971, 452, 1011, 197, 1011], "score": 0.9}, {"category_id": 1, "poly": [196, 1194, 422, 1194, 422, 1232, 196, 1232], "score": 0.877}, {"category_id": 1, "poly": [198, 453, 860, 453, 860, 492, 198, 492], "score": 0.865}, {"category_id": 1, "poly": [215, 1137, 815, 1137, 815, 1178, 215, 1178], "score": 0.851}, {"category_id": 1, "poly": [444, 1514, 1018, 1514, 1018, 1693, 444, 1693], "score": 0.84}, {"category_id": 1, "poly": [217, 1026, 1474, 1026, 1474, 1122, 217, 1122], "score": 0.835}, {"category_id": 0, "poly": [154, 791, 421, 791, 421, 833, 154, 833], "score": 0.829}, {"category_id": 0, "poly": [153, 274, 420, 274, 420, 316, 153, 316], "score": 0.807}, {"category_id": 2, "poly": [156, 110, 352, 110, 352, 244, 156, 244], "score": 0.292}, {"category_id": 13, "poly": [368, 1417, 439, 1417, 439, 1456, 368, 1456], "score": 0.93, "latex": "N _ { n o m }"}, {"category_id": 13, "poly": [1293, 1417, 1347, 1417, 1347, 1456, 1293, 1456], "score": 0.92, "latex": "\\theta _ { a c t }"}, {"category_id": 13, "poly": [1022, 1417, 1081, 1417, 1081, 1456, 1022, 1456], "score": 0.92, "latex": "N _ { a c t }"}, {"category_id": 13, "poly": [1293, 1362, 1331, 1362, 1331, 1399, 1293, 1399], "score": 0.91, "latex": "N _ { E }"}, {"category_id": 13, "poly": [1073, 1306, 1127, 1306, 1127, 1346, 1073, 1346], "score": 0.9, "latex": "N _ { a l l }"}, {"category_id": 13, "poly": [682, 1416, 750, 1416, 750, 1459, 682, 1459], "score": 0.86, "latex": "\\theta _ { n o m }"}, {"category_id": 13, "poly": [945, 1362, 990, 1362, 990, 1399, 945, 1399], "score": 0.78, "latex": "( N _ { 2 }"}, {"category_id": 13, "poly": [702, 1362, 742, 1362, 742, 1400, 702, 1400], "score": 0.77, "latex": "{ \\mathbf { } } \\cdot N _ { 1 }"}, {"category_id": 14, "poly": [446, 1651, 634, 1651, 634, 1690, 446, 1690], "score": 0.61, "latex": "N _ { E } = N _ { 1 } + N _ { 2 }"}, {"category_id": 14, "poly": [442, 1510, 700, 1510, 700, 1693, 442, 1693], "score": 0.35, "latex": "\\begin{array} { r l } & { \\theta _ { a c t } = \\frac { N _ { a c t } } { N _ { n o m } } * \\theta _ { n o m } } \\\\ & { N _ { n o m } = N _ { a l l } - N _ { E } } \\\\ & { N _ { E } = N _ { 1 } + N _ { 2 } } \\end{array}"}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.26, "latex": "\\circledcirc"}, {"category_id": 14, "poly": [445, 1588, 694, 1588, 694, 1634, 445, 1634], "score": 0.25, "latex": "N _ { n o m } = N _ { a l l } - N _ { E }"}, {"category_id": 15, "poly": [493.0, 2219.0, 1235.0, 2219.0, 1235.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1362.0, 2218.0, 1479.0, 2218.0, 1479.0, 2264.0, 1362.0, 2264.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2222.0, 320.0, 2222.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 791.0, 421.0, 791.0, 421.0, 836.0, 152.0, 836.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 273.0, 421.0, 273.0, 421.0, 318.0, 152.0, 318.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 191.0, 284.0, 191.0, 284.0, 232.0, 219.0, 232.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [213.0, 1246.0, 1483.0, 1246.0, 1483.0, 1289.0, 213.0, 1289.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [211.0, 1297.0, 1072.0, 1297.0, 1072.0, 1351.0, 211.0, 1351.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1128.0, 1297.0, 1143.0, 1297.0, 1143.0, 1351.0, 1128.0, 1351.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [213.0, 1356.0, 701.0, 1356.0, 701.0, 1400.0, 213.0, 1400.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [743.0, 1356.0, 944.0, 1356.0, 944.0, 1400.0, 743.0, 1400.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [991.0, 1356.0, 1292.0, 1356.0, 1292.0, 1400.0, 991.0, 1400.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1332.0, 1356.0, 1488.0, 1356.0, 1488.0, 1400.0, 1332.0, 1400.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [209.0, 1403.0, 367.0, 1403.0, 367.0, 1466.0, 209.0, 1466.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [440.0, 1403.0, 681.0, 1403.0, 681.0, 1466.0, 440.0, 1466.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [751.0, 1403.0, 768.0, 1403.0, 768.0, 1466.0, 751.0, 1466.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [771.0, 1411.0, 1021.0, 1411.0, 1021.0, 1456.0, 771.0, 1456.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1082.0, 1411.0, 1292.0, 1411.0, 1292.0, 1456.0, 1082.0, 1456.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1348.0, 1411.0, 1476.0, 1411.0, 1476.0, 1456.0, 1348.0, 1456.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [210.0, 1467.0, 300.0, 1467.0, 300.0, 1514.0, 210.0, 1514.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [452.75, 1490.5, 483.75, 1490.5, 483.75, 1502.5, 452.75, 1502.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 510.0, 836.0, 510.0, 836.0, 546.0, 197.0, 546.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [200.0, 565.0, 861.0, 565.0, 861.0, 600.0, 200.0, 600.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [200.0, 622.0, 554.0, 622.0, 554.0, 656.0, 200.0, 656.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [201.0, 678.0, 640.0, 678.0, 640.0, 712.0, 201.0, 712.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [200.0, 732.0, 983.0, 732.0, 983.0, 769.0, 200.0, 769.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [195.0, 971.0, 451.0, 971.0, 451.0, 1009.0, 195.0, 1009.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [195.0, 1194.0, 421.0, 1194.0, 421.0, 1232.0, 195.0, 1232.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [201.0, 454.0, 856.0, 454.0, 856.0, 491.0, 201.0, 491.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [213.0, 1137.0, 816.0, 1137.0, 816.0, 1182.0, 213.0, 1182.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [905.0, 1514.0, 1013.0, 1514.0, 1013.0, 1564.0, 905.0, 1564.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [910.0, 1585.0, 1015.0, 1585.0, 1015.0, 1632.0, 910.0, 1632.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [917.0, 1645.0, 1023.0, 1645.0, 1023.0, 1692.0, 917.0, 1692.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [448.0, 1518.5, 505.0, 1518.5, 505.0, 1567.5, 448.0, 1567.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [544.0, 1508.5, 606.0, 1508.5, 606.0, 1545.5, 544.0, 1545.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [631.75, 1525.5, 695.75, 1525.5, 695.75, 1566.0, 631.75, 1566.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [540.0, 1543.5, 608.0, 1543.5, 608.0, 1579.5, 540.0, 1579.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [651.0, 1520.5, 680.0, 1520.5, 680.0, 1530.5, 651.0, 1530.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [448.75, 1593.0, 527.75, 1593.0, 527.75, 1637.0, 448.75, 1637.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [212.0, 1027.0, 1474.0, 1027.0, 1474.0, 1062.0, 212.0, 1062.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [214.0, 1082.0, 930.0, 1082.0, 930.0, 1121.0, 214.0, 1121.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [994.0, 1108.0, 1012.0, 1108.0, 1012.0, 1128.0, 994.0, 1128.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1068.75, 1083.5, 1122.75, 1083.5, 1122.75, 1098.0, 1068.75, 1098.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 12, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [248, 1660, 1405, 1660, 1405, 1779, 248, 1779], "score": 0.974, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-20</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [189, 2053, 1465, 2053, 1465, 2167, 189, 2167], "score": 0.974, "html": "<table><tr><td>序列</td><td>模式</td><td>三通水阀比率 (%)</td><td>角度范围 （°)</td><td>脈冲数</td><td>示意图</td><td>备注</td></tr></table>"}, {"category_id": 5, "poly": [249, 1212, 1407, 1212, 1407, 1329, 249, 1329], "score": 0.971, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 19</td><td>SAIC_EWV_3WAY_ RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 3, "poly": [236, 264, 1456, 264, 1456, 760, 236, 760], "score": 0.958}, {"category_id": 1, "poly": [225, 1846, 672, 1846, 672, 1997, 225, 1997], "score": 0.928}, {"category_id": 6, "poly": [611, 2019, 1093, 2019, 1093, 2049, 611, 2049], "score": 0.916}, {"category_id": 4, "poly": [684, 786, 1021, 786, 1021, 822, 684, 822], "score": 0.914}, {"category_id": 1, "poly": [199, 1791, 584, 1791, 584, 1829, 199, 1829], "score": 0.893}, {"category_id": 1, "poly": [197, 837, 604, 837, 604, 876, 197, 876], "score": 0.886}, {"category_id": 1, "poly": [224, 892, 1077, 892, 1077, 933, 224, 933], "score": 0.882}, {"category_id": 2, "poly": [460, 2221, 1250, 2221, 1250, 2262, 460, 2262], "score": 0.87}, {"category_id": 2, "poly": [1368, 2224, 1472, 2224, 1472, 2259, 1368, 2259], "score": 0.859}, {"category_id": 1, "poly": [233, 948, 1053, 948, 1053, 1100, 233, 1100], "score": 0.854}, {"category_id": 1, "poly": [199, 1342, 1383, 1342, 1383, 1382, 199, 1382], "score": 0.853}, {"category_id": 1, "poly": [235, 1116, 742, 1116, 742, 1155, 235, 1155], "score": 0.851}, {"category_id": 0, "poly": [155, 1165, 464, 1165, 464, 1208, 155, 1208], "score": 0.82}, {"category_id": 2, "poly": [187, 2223, 318, 2223, 318, 2259, 187, 2259], "score": 0.81}, {"category_id": 0, "poly": [155, 1614, 546, 1614, 546, 1657, 155, 1657], "score": 0.762}, {"category_id": 1, "poly": [224, 1397, 1109, 1397, 1109, 1604, 224, 1604], "score": 0.57}, {"category_id": 2, "poly": [156, 112, 348, 112, 348, 241, 156, 241], "score": 0.468}, {"category_id": 2, "poly": [775, 187, 1488, 187, 1488, 231, 775, 231], "score": 0.243}, {"category_id": 1, "poly": [155, 1614, 546, 1614, 546, 1657, 155, 1657], "score": 0.162}, {"category_id": 1, "poly": [1339, 120, 1454, 120, 1454, 165, 1339, 165], "score": 0.101}, {"category_id": 13, "poly": [449, 949, 603, 949, 603, 989, 449, 989], "score": 0.9, "latex": "I _ { a c t } { > } 0 . 2 5 \\mathsf { A }"}, {"category_id": 13, "poly": [201, 1905, 356, 1905, 356, 1939, 201, 1939], "score": 0.87, "latex": "\\triangleright 0 \\% \\sim 1 0 0 \\%"}, {"category_id": 13, "poly": [726, 950, 857, 950, 857, 986, 726, 986], "score": 0.85, "latex": "\\mathsf { t } { > } 1 0 0 \\mathsf { m s }"}, {"category_id": 13, "poly": [452, 1848, 515, 1848, 515, 1883, 452, 1883], "score": 0.85, "latex": "3 \\cdot > 1"}, {"category_id": 13, "poly": [466, 1905, 530, 1905, 530, 1939, 466, 1939], "score": 0.82, "latex": "3 \\cdot > 1"}, {"category_id": 13, "poly": [482, 1959, 546, 1959, 546, 1994, 482, 1994], "score": 0.81, "latex": "3 \\cdot > 1"}, {"category_id": 13, "poly": [296, 1848, 359, 1848, 359, 1883, 296, 1883], "score": 0.77, "latex": "_ { 2 \\cdot > 1 }"}, {"category_id": 13, "poly": [993, 1455, 1071, 1455, 1071, 1492, 993, 1492], "score": 0.76, "latex": "2 0 ^ { \\circ } / s ,"}, {"category_id": 13, "poly": [327, 1959, 390, 1959, 390, 1994, 327, 1994], "score": 0.7, "latex": "_ { 2 \\cdot > 1 }"}, {"category_id": 13, "poly": [381, 1905, 441, 1905, 441, 1939, 381, 1939], "score": 0.67, "latex": "_ { 2 \\cdot > 1 }"}, {"category_id": 13, "poly": [229, 1849, 273, 1849, 273, 1883, 229, 1883], "score": 0.62, "latex": "0 \\%"}, {"category_id": 13, "poly": [230, 1959, 303, 1959, 303, 1994, 230, 1994], "score": 0.58, "latex": "100 \\%"}, {"category_id": 13, "poly": [477, 1399, 538, 1399, 538, 1435, 477, 1435], "score": 0.57, "latex": "{ < } 8 5"}, {"category_id": 13, "poly": [586, 2114, 642, 2114, 642, 2146, 586, 2146], "score": 0.43, "latex": "( \\% )"}, {"category_id": 13, "poly": [458, 2223, 491, 2223, 491, 2258, 458, 2258], "score": 0.41, "latex": "\\circledcirc"}, {"category_id": 13, "poly": [196, 1565, 227, 1565, 227, 1600, 196, 1600], "score": 0.27, "latex": "\\gtrdot"}, {"category_id": 13, "poly": [231, 949, 263, 949, 263, 984, 231, 984], "score": 0.26, "latex": "\\curlyeqsucc"}, {"category_id": 15, "poly": [230.0, 266.0, 376.0, 266.0, 376.0, 323.0, 230.0, 323.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1315.0, 271.0, 1455.0, 271.0, 1455.0, 319.0, 1315.0, 319.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [378.0, 347.0, 399.0, 347.0, 399.0, 375.0, 378.0, 375.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1286.0, 355.0, 1297.0, 355.0, 1297.0, 367.0, 1286.0, 367.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [435.0, 380.0, 504.0, 380.0, 504.0, 432.0, 435.0, 432.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1125.0, 383.0, 1252.0, 383.0, 1252.0, 432.0, 1125.0, 432.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [334.0, 476.0, 381.0, 476.0, 381.0, 521.0, 334.0, 521.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [432.0, 471.0, 484.0, 471.0, 484.0, 520.0, 432.0, 520.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1161.0, 468.0, 1250.0, 468.0, 1250.0, 520.0, 1161.0, 520.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [457.0, 577.0, 516.0, 577.0, 516.0, 632.0, 457.0, 632.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1179.0, 568.0, 1241.0, 568.0, 1241.0, 629.0, 1179.0, 629.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [680.0, 714.0, 878.0, 714.0, 878.0, 762.0, 680.0, 762.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1315.75, 335.0, 1404.75, 335.0, 1404.75, 397.5, 1315.75, 397.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1090.0, 475.5, 1174.0, 475.5, 1174.0, 526.0, 1090.0, 526.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1312.0, 477.0, 1391.0, 477.0, 1391.0, 528.5, 1312.0, 528.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [609.0, 2019.0, 1092.0, 2019.0, 1092.0, 2050.0, 609.0, 2050.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [684.0, 787.0, 1019.0, 787.0, 1019.0, 821.0, 684.0, 821.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [492.0, 2219.0, 1235.0, 2219.0, 1235.0, 2265.0, 492.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1363.0, 2218.0, 1478.0, 2218.0, 1478.0, 2264.0, 1363.0, 2264.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [150.0, 1161.0, 465.0, 1161.0, 465.0, 1214.0, 150.0, 1214.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2221.0, 320.0, 2221.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 1609.0, 549.0, 1609.0, 549.0, 1661.0, 151.0, 1661.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 188.0, 285.0, 188.0, 285.0, 232.0, 219.0, 232.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [778.0, 185.0, 1485.0, 185.0, 1485.0, 228.0, 778.0, 228.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [274.0, 1846.0, 295.0, 1846.0, 295.0, 1883.0, 274.0, 1883.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [360.0, 1846.0, 451.0, 1846.0, 451.0, 1883.0, 360.0, 1883.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [516.0, 1846.0, 595.0, 1846.0, 595.0, 1883.0, 516.0, 1883.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [357.0, 1905.0, 380.0, 1905.0, 380.0, 1939.0, 357.0, 1939.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [442.0, 1905.0, 465.0, 1905.0, 465.0, 1939.0, 442.0, 1939.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [531.0, 1905.0, 674.0, 1905.0, 674.0, 1939.0, 531.0, 1939.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [304.0, 1959.0, 326.0, 1959.0, 326.0, 1994.0, 304.0, 1994.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [391.0, 1959.0, 481.0, 1959.0, 481.0, 1994.0, 391.0, 1994.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [547.0, 1959.0, 630.0, 1959.0, 630.0, 1994.0, 547.0, 1994.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [201.0, 1791.0, 581.0, 1791.0, 581.0, 1828.0, 201.0, 1828.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 839.0, 599.0, 839.0, 599.0, 874.0, 197.0, 874.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [234.0, 892.0, 1077.0, 892.0, 1077.0, 932.0, 234.0, 932.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [264.0, 947.0, 448.0, 947.0, 448.0, 990.0, 264.0, 990.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [604.0, 947.0, 725.0, 947.0, 725.0, 990.0, 604.0, 990.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [858.0, 947.0, 870.0, 947.0, 870.0, 990.0, 858.0, 990.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [234.0, 1003.0, 1053.0, 1003.0, 1053.0, 1044.0, 234.0, 1044.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [230.0, 1052.0, 821.0, 1052.0, 821.0, 1105.0, 230.0, 1105.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [200.0, 1342.0, 1386.0, 1342.0, 1386.0, 1382.0, 200.0, 1382.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [235.0, 1115.0, 744.0, 1115.0, 744.0, 1156.0, 235.0, 1156.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [234.0, 1400.0, 476.0, 1400.0, 476.0, 1433.0, 234.0, 1433.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [539.0, 1400.0, 551.0, 1400.0, 551.0, 1433.0, 539.0, 1433.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [232.0, 1455.0, 992.0, 1455.0, 992.0, 1491.0, 232.0, 1491.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1072.0, 1455.0, 1107.0, 1455.0, 1107.0, 1491.0, 1072.0, 1491.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [234.0, 1508.0, 598.0, 1508.0, 598.0, 1546.0, 234.0, 1546.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [232.0, 1567.0, 986.0, 1567.0, 986.0, 1601.0, 232.0, 1601.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 1609.0, 549.0, 1609.0, 549.0, 1661.0, 151.0, 1661.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1334.0, 114.0, 1446.0, 114.0, 1446.0, 175.0, 1334.0, 175.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 13, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [185, 261, 1467, 261, 1467, 1187, 185, 1187], "score": 0.974, "html": "<table><tr><td>1</td><td>起始端限位</td><td>/</td><td>0°</td><td>0</td><td></td><td></td></tr><tr><td>2</td><td>/</td><td>0%</td><td>10°</td><td>74</td><td>B O</td><td></td></tr><tr><td>3</td><td></td><td>0%~100%</td><td>10°~80°</td><td></td><td>3* 2</td><td></td></tr><tr><td>4</td><td>/</td><td>100%</td><td>80° -</td><td>590</td><td>3 2</td><td></td></tr><tr><td>5</td><td>终止端限位</td><td></td><td>90°</td><td>664</td><td></td><td></td></tr></table>"}, {"category_id": 5, "poly": [248, 1241, 1406, 1241, 1406, 1359, 248, 1359], "score": 0.968, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 21</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 1, "poly": [196, 2076, 823, 2076, 823, 2120, 196, 2120], "score": 0.937}, {"category_id": 2, "poly": [457, 2221, 1236, 2221, 1236, 2262, 457, 2262], "score": 0.933}, {"category_id": 2, "poly": [1368, 2224, 1472, 2224, 1472, 2258, 1368, 2258], "score": 0.924}, {"category_id": 4, "poly": [647, 2027, 1055, 2027, 1055, 2062, 647, 2062], "score": 0.916}, {"category_id": 1, "poly": [197, 1369, 1220, 1369, 1220, 1410, 197, 1410], "score": 0.909}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2258, 187, 2258], "score": 0.904}, {"category_id": 0, "poly": [153, 1193, 387, 1193, 387, 1238, 153, 1238], "score": 0.86}, {"category_id": 2, "poly": [158, 113, 350, 113, 350, 241, 158, 241], "score": 0.815}, {"category_id": 3, "poly": [547, 1425, 1073, 1425, 1073, 1998, 547, 1998], "score": 0.644}, {"category_id": 1, "poly": [1338, 121, 1483, 121, 1483, 166, 1338, 166], "score": 0.593}, {"category_id": 3, "poly": [217, 1424, 1076, 1424, 1076, 1998, 217, 1998], "score": 0.385}, {"category_id": 2, "poly": [775, 186, 1489, 186, 1489, 231, 775, 231], "score": 0.377}, {"category_id": 13, "poly": [561, 2078, 624, 2078, 624, 2118, 561, 2118], "score": 0.91, "latex": "N _ { a c t }"}, {"category_id": 13, "poly": [383, 2078, 440, 2078, 440, 2118, 383, 2118], "score": 0.91, "latex": "\\theta _ { a c t }"}, {"category_id": 13, "poly": [458, 2223, 491, 2223, 491, 2258, 458, 2258], "score": 0.44, "latex": "\\circledcirc"}, {"category_id": 15, "poly": [492.0, 2219.0, 1235.0, 2219.0, 1235.0, 2265.0, 492.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1363.0, 2218.0, 1478.0, 2218.0, 1478.0, 2264.0, 1363.0, 2264.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [650.0, 2030.0, 1052.0, 2030.0, 1052.0, 2059.0, 650.0, 2059.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2222.0, 320.0, 2222.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [148.0, 1190.0, 392.0, 1190.0, 392.0, 1243.0, 148.0, 1243.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 189.0, 284.0, 189.0, 284.0, 230.0, 219.0, 230.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [650.0, 1419.0, 810.0, 1419.0, 810.0, 1468.0, 650.0, 1468.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [644.0, 1603.0, 805.0, 1603.0, 805.0, 1659.0, 644.0, 1659.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [910.0, 1605.0, 1076.0, 1605.0, 1076.0, 1657.0, 910.0, 1657.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [628.0, 1652.0, 814.0, 1652.0, 814.0, 1708.0, 628.0, 1708.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [929.0, 1660.0, 1059.0, 1660.0, 1059.0, 1714.0, 929.0, 1714.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [686.0, 1945.0, 783.0, 1945.0, 783.0, 1997.0, 686.0, 1997.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [652.0, 1483.0, 665.0, 1483.0, 665.0, 1496.0, 652.0, 1496.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [649.0, 1419.0, 809.0, 1419.0, 809.0, 1468.0, 649.0, 1468.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [644.0, 1602.0, 806.0, 1602.0, 806.0, 1658.0, 644.0, 1658.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [910.0, 1607.0, 1075.0, 1607.0, 1075.0, 1656.0, 910.0, 1656.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [628.0, 1653.0, 815.0, 1653.0, 815.0, 1709.0, 628.0, 1709.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [930.0, 1659.0, 1058.0, 1659.0, 1058.0, 1717.0, 930.0, 1717.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [686.0, 1946.0, 781.0, 1946.0, 781.0, 1995.0, 686.0, 1995.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [778.0, 187.0, 1487.0, 187.0, 1487.0, 227.0, 778.0, 227.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [196.0, 2076.0, 382.0, 2076.0, 382.0, 2122.0, 196.0, 2122.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [441.0, 2076.0, 560.0, 2076.0, 560.0, 2122.0, 441.0, 2122.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [625.0, 2076.0, 822.0, 2076.0, 822.0, 2122.0, 625.0, 2122.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 1369.0, 1223.0, 1369.0, 1223.0, 1409.0, 197.0, 1409.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1335.0, 115.0, 1497.0, 115.0, 1497.0, 173.0, 1335.0, 173.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 14, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [255, 660, 1398, 660, 1398, 1364, 255, 1364], "score": 0.984, "html": "<table><tr><td>信号定义</td><td>故障类型</td><td>故障优先级</td><td>故障判定前置条件</td><td>备注</td></tr><tr><td rowspan=\"7\">电机诊断 Fault_Signal</td><td>过流故障</td><td>1</td><td>全程检测</td><td></td></tr><tr><td rowspan=\"3\">堵转故障</td><td>2（电机堵转）</td><td>全程检测</td><td></td></tr><tr><td>2（阀芯角度低于量程）</td><td>自校正检测</td><td></td></tr><tr><td>2 （传感器异常）</td><td>全程检测 - -</td><td></td></tr><tr><td>过温故障</td><td>3</td><td>全程检测 -</td><td></td></tr><tr><td rowspan=\"2\">开路故障</td><td>4（电机开路）</td><td>全程检测</td><td></td></tr><tr><td>4（阀芯角度超量程）</td><td>自校正检测</td><td></td></tr><tr><td rowspan=\"2\">VoltageErr</td><td>过压故障</td><td>-</td><td>全程检测</td><td></td></tr><tr><td>欠压故障</td><td></td><td>全程检测</td><td></td></tr><tr><td>RespError</td><td>LIN通信故障</td><td></td><td>全程检测</td><td></td></tr></table>"}, {"category_id": 5, "poly": [171, 1660, 1478, 1660, 1478, 2152, 171, 2152], "score": 0.982, "html": "<table><tr><td>故脉</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复</td><td>重启策略</td></tr><tr><td>线图</td><td>全程检测</td><td>MOs管降</td><td>1)线圈短路故障判定成立后水 阀停转，2s后尝试重启； 2) 第5次重启失败之后，EWV 反线圈短路故障，且再 3) 直至重启合计10次，则在本 工作周期内不在执行重启，同 时不响应VCU的位置请求；同 时位置反馈信息为当前位置。</td><td>VDs&lt;1.8V</td><td>重启次数及时间： 每次问满， 停止重启。</td></tr></table>"}, {"category_id": 5, "poly": [252, 492, 1407, 492, 1407, 611, 252, 611], "score": 0.973, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-22</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [249, 1488, 1405, 1488, 1405, 1607, 249, 1607], "score": 0.973, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-23</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 1, "poly": [155, 340, 1483, 340, 1483, 434, 155, 434], "score": 0.944}, {"category_id": 2, "poly": [459, 2221, 1236, 2221, 1236, 2262, 459, 2262], "score": 0.927}, {"category_id": 2, "poly": [1368, 2224, 1472, 2224, 1472, 2259, 1368, 2259], "score": 0.925}, {"category_id": 6, "poly": [698, 628, 1008, 628, 1008, 659, 698, 659], "score": 0.909}, {"category_id": 2, "poly": [187, 2223, 318, 2223, 318, 2259, 187, 2259], "score": 0.88}, {"category_id": 0, "poly": [153, 445, 388, 445, 388, 490, 153, 490], "score": 0.851}, {"category_id": 8, "poly": [550, 264, 861, 264, 861, 331, 550, 331], "score": 0.838}, {"category_id": 0, "poly": [155, 1379, 702, 1379, 702, 1423, 155, 1423], "score": 0.816}, {"category_id": 1, "poly": [192, 1618, 1383, 1618, 1383, 1656, 192, 1656], "score": 0.762}, {"category_id": 2, "poly": [155, 110, 351, 110, 351, 244, 155, 244], "score": 0.564}, {"category_id": 0, "poly": [156, 1444, 647, 1444, 647, 1483, 156, 1483], "score": 0.496}, {"category_id": 1, "poly": [1007, 277, 1105, 277, 1105, 316, 1007, 316], "score": 0.487}, {"category_id": 5, "poly": [153, 108, 1496, 108, 1496, 247, 153, 247], "score": 0.447, "html": "<table><tr><td>鹏翎 NE</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 1, "poly": [156, 1444, 647, 1444, 647, 1483, 156, 1483], "score": 0.414}, {"category_id": 6, "poly": [192, 1618, 1383, 1618, 1383, 1656, 192, 1656], "score": 0.161}, {"category_id": 1, "poly": [155, 1379, 702, 1379, 702, 1423, 155, 1423], "score": 0.143}, {"category_id": 2, "poly": [777, 187, 1491, 187, 1491, 231, 777, 231], "score": 0.129}, {"category_id": 0, "poly": [550, 264, 861, 264, 861, 331, 550, 331], "score": 0.1}, {"category_id": 14, "poly": [548, 261, 861, 261, 861, 331, 548, 331], "score": 0.92, "latex": "\\begin{array} { r } { N _ { a c t } = \\frac { \\dot { \\theta _ { a c t } ^ { \\circ } } } { 3 6 0 ^ { \\circ } } * \\frac { 1 } { K } * n * 2 } \\end{array}"}, {"category_id": 13, "poly": [288, 341, 348, 341, 348, 381, 288, 381], "score": 0.91, "latex": "\\theta _ { a c t }"}, {"category_id": 13, "poly": [897, 342, 959, 342, 959, 377, 897, 377], "score": 0.87, "latex": "3 6 0 ^ { \\circ }"}, {"category_id": 13, "poly": [455, 1956, 589, 1956, 589, 1990, 455, 1990], "score": 0.75, "latex": "\\mathsf { V } _ { \\mathsf { D } \\mathsf { S } } { \\mathsf { - } } 1 . 8 \\mathsf { V }"}, {"category_id": 13, "poly": [1044, 1935, 1175, 1935, 1175, 1970, 1044, 1970], "score": 0.34, "latex": "\\mathsf { N } _ { \\mathsf { D } \\mathsf { S } } { < } 1 . 8 \\mathsf { V }"}, {"category_id": 13, "poly": [458, 2223, 491, 2223, 491, 2258, 458, 2258], "score": 0.33, "latex": "\\circledcirc"}, {"category_id": 15, "poly": [492.0, 2219.0, 1234.0, 2219.0, 1234.0, 2265.0, 492.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1363.0, 2218.0, 1480.0, 2218.0, 1480.0, 2264.0, 1363.0, 2264.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [699.0, 628.0, 1006.0, 628.0, 1006.0, 660.0, 699.0, 660.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2221.0, 320.0, 2221.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [148.0, 442.0, 392.0, 442.0, 392.0, 495.0, 148.0, 495.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [150.0, 1374.0, 705.0, 1374.0, 705.0, 1427.0, 150.0, 1427.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 191.0, 284.0, 191.0, 284.0, 232.0, 219.0, 232.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 1442.0, 649.0, 1442.0, 649.0, 1486.0, 152.0, 1486.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 1615.0, 1389.0, 1615.0, 1389.0, 1657.0, 197.0, 1657.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [779.0, 185.0, 1486.0, 185.0, 1486.0, 228.0, 779.0, 228.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [649.0, 247.0, 723.0, 247.0, 723.0, 313.0, 649.0, 313.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [549.0, 278.5, 620.0, 278.5, 620.0, 326.5, 549.0, 326.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [645.25, 294.5, 719.25, 294.5, 719.25, 338.5, 645.25, 338.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [194.0, 337.0, 287.0, 337.0, 287.0, 384.0, 194.0, 384.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [349.0, 337.0, 896.0, 337.0, 896.0, 384.0, 349.0, 384.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [960.0, 337.0, 1481.0, 337.0, 1481.0, 384.0, 960.0, 384.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [150.0, 396.0, 812.0, 396.0, 812.0, 432.0, 150.0, 432.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 1615.0, 1389.0, 1615.0, 1389.0, 1657.0, 197.0, 1657.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1003.0, 274.0, 1108.0, 274.0, 1108.0, 325.0, 1003.0, 325.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 1442.0, 649.0, 1442.0, 649.0, 1486.0, 152.0, 1486.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [150.0, 1374.0, 705.0, 1374.0, 705.0, 1427.0, 150.0, 1427.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 15, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [171, 1461, 1504, 1461, 1504, 2055, 171, 2055], "score": 0.98, "html": "<table><tr><td>故障</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td>堵转 故障</td><td>电机堵转</td><td>电机运行电流I</td><td>1)堵转判定成立后 水阀停止运行，2s后 尝试重启。 2) 第5次重启失败之 后，EWV开始上报堵 转故障，并再次执行 重启； 3)直至重启合计10 次，则在本工作周期 内不在行向自</td><td>1)电机运行电流 0.1A≤I≤0.4A，持 续&gt; 0ms 以 2)阀体重启成功 后,水阀开始进 行到0°）；运行 到0°后按照VCU 指令运行到目标</td><td>重启次数机时 间： 每次重启间隔 2s；重启10次 无法恢复，则 停止重启。</td></tr></table>"}, {"category_id": 3, "poly": [258, 344, 1454, 344, 1454, 1036, 258, 1036], "score": 0.969}, {"category_id": 5, "poly": [249, 1289, 1408, 1289, 1408, 1406, 249, 1406], "score": 0.966, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 24</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 1, "poly": [146, 1046, 1493, 1046, 1493, 1127, 146, 1127], "score": 0.934}, {"category_id": 2, "poly": [457, 2221, 1236, 2221, 1236, 2262, 457, 2262], "score": 0.926}, {"category_id": 2, "poly": [1368, 2224, 1472, 2224, 1472, 2258, 1368, 2258], "score": 0.92}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2259, 187, 2259], "score": 0.893}, {"category_id": 1, "poly": [191, 265, 1204, 265, 1204, 304, 191, 304], "score": 0.87}, {"category_id": 1, "poly": [183, 1420, 1258, 1420, 1258, 1456, 183, 1456], "score": 0.718}, {"category_id": 2, "poly": [156, 108, 1494, 108, 1494, 246, 156, 246], "score": 0.715}, {"category_id": 8, "poly": [191, 1128, 679, 1128, 679, 1233, 191, 1233], "score": 0.656}, {"category_id": 1, "poly": [194, 310, 265, 310, 265, 343, 194, 343], "score": 0.616}, {"category_id": 0, "poly": [154, 1243, 536, 1243, 536, 1283, 154, 1283], "score": 0.483}, {"category_id": 1, "poly": [191, 1128, 679, 1128, 679, 1233, 191, 1233], "score": 0.438}, {"category_id": 2, "poly": [160, 115, 348, 115, 348, 240, 160, 240], "score": 0.41}, {"category_id": 6, "poly": [183, 1420, 1258, 1420, 1258, 1456, 183, 1456], "score": 0.193}, {"category_id": 13, "poly": [490, 1049, 624, 1049, 624, 1082, 490, 1082], "score": 0.91, "latex": "\\mathsf { V } _ { \\mathsf { D } \\mathsf { S } } { \\mathsf { - } } 1 . 8 \\mathsf { V } \\mathsf { S }"}, {"category_id": 13, "poly": [350, 1092, 447, 1092, 447, 1126, 350, 1126], "score": 0.83, "latex": "I _ { M O S M A X }"}, {"category_id": 14, "poly": [190, 1179, 680, 1179, 680, 1232, 190, 1232], "score": 0.81, "latex": "\\begin{array} { r } { I _ { M O S M I N } = \\frac { V _ { R E F } } { G _ { A I N ^ { * R } M O S M A X } } = \\frac { 1 . 8 } { 1 * 1 . 1 5 } = 1 . 5 7 A } \\end{array}"}, {"category_id": 13, "poly": [1027, 1625, 1212, 1625, 1212, 1659, 1027, 1659], "score": 0.65, "latex": "0 . 1 \\mathsf { A } \\leqslant 1 \\leqslant 0 . 4 \\mathsf { A }"}, {"category_id": 13, "poly": [190, 1127, 633, 1127, 633, 1180, 190, 1180], "score": 0.54, "latex": "\\begin{array} { r } { I _ { M O S M A X } = \\frac { V _ { R E F } } { G _ { A I N ^ { * R } M O S M I N } } = \\frac { 1 . 8 } { 1 * 0 . 6 } = 3 A } \\end{array}"}, {"category_id": 13, "poly": [807, 268, 885, 268, 885, 303, 807, 303], "score": 0.43, "latex": "\\mathsf { A } / \\mathsf { B } / \\mathsf { C }"}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.39, "latex": "\\circledcirc"}, {"category_id": 13, "poly": [495, 1784, 586, 1784, 586, 1815, 495, 1815], "score": 0.28, "latex": "> 0 . 4 \\mathsf { A }"}, {"category_id": 13, "poly": [639, 1092, 732, 1092, 732, 1127, 639, 1127], "score": 0.27, "latex": ". I _ { M O S M I N }"}, {"category_id": 15, "poly": [1303.0, 341.0, 1442.0, 341.0, 1442.0, 381.0, 1303.0, 381.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [354.0, 371.0, 585.0, 371.0, 585.0, 405.0, 354.0, 405.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [352.0, 401.0, 423.0, 401.0, 423.0, 434.0, 352.0, 434.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [398.0, 437.0, 446.0, 437.0, 446.0, 480.0, 398.0, 480.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [837.0, 451.0, 1015.0, 451.0, 1015.0, 500.0, 837.0, 500.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [263.0, 542.0, 311.0, 542.0, 311.0, 577.0, 263.0, 577.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [442.0, 533.0, 481.0, 533.0, 481.0, 566.0, 442.0, 566.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [525.0, 481.0, 617.0, 481.0, 617.0, 555.0, 525.0, 555.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1212.0, 480.0, 1282.0, 480.0, 1282.0, 532.0, 1212.0, 532.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [263.0, 569.0, 436.0, 569.0, 436.0, 605.0, 263.0, 605.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [527.0, 570.0, 615.0, 570.0, 615.0, 607.0, 527.0, 607.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [667.0, 569.0, 741.0, 569.0, 741.0, 608.0, 667.0, 608.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [766.0, 568.0, 845.0, 568.0, 845.0, 610.0, 766.0, 610.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [907.0, 576.0, 918.0, 576.0, 918.0, 587.0, 907.0, 587.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [959.0, 566.0, 1013.0, 566.0, 1013.0, 602.0, 959.0, 602.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [262.0, 598.0, 350.0, 598.0, 350.0, 635.0, 262.0, 635.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [531.0, 599.0, 618.0, 599.0, 618.0, 636.0, 531.0, 636.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [667.0, 596.0, 754.0, 596.0, 754.0, 638.0, 667.0, 638.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [938.0, 609.0, 1002.0, 609.0, 1002.0, 649.0, 938.0, 649.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1215.0, 642.0, 1222.0, 642.0, 1222.0, 649.0, 1215.0, 649.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1303.0, 617.0, 1364.0, 617.0, 1364.0, 652.0, 1303.0, 652.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [531.0, 690.0, 614.0, 690.0, 614.0, 727.0, 531.0, 727.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [667.0, 691.0, 744.0, 691.0, 744.0, 730.0, 667.0, 730.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [762.0, 687.0, 841.0, 687.0, 841.0, 730.0, 762.0, 730.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [907.0, 697.0, 918.0, 697.0, 918.0, 706.0, 907.0, 706.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [957.0, 686.0, 1013.0, 686.0, 1013.0, 722.0, 957.0, 722.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [531.0, 720.0, 618.0, 720.0, 618.0, 755.0, 531.0, 755.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [666.0, 719.0, 756.0, 719.0, 756.0, 760.0, 666.0, 760.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [937.0, 727.0, 999.0, 727.0, 999.0, 767.0, 937.0, 767.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [529.0, 769.0, 597.0, 769.0, 597.0, 812.0, 529.0, 812.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [526.0, 798.0, 620.0, 798.0, 620.0, 843.0, 526.0, 843.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [402.0, 826.0, 433.0, 826.0, 433.0, 855.0, 402.0, 855.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [835.0, 828.0, 1017.0, 828.0, 1017.0, 878.0, 835.0, 878.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [352.0, 895.0, 604.0, 895.0, 604.0, 939.0, 352.0, 939.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [351.0, 931.0, 421.0, 931.0, 421.0, 964.0, 351.0, 964.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [675.0, 997.0, 904.0, 997.0, 904.0, 1029.0, 675.0, 1029.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1068.0, 1008.0, 1202.0, 1008.0, 1202.0, 1043.0, 1068.0, 1043.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1287.0, 1001.0, 1364.0, 1001.0, 1364.0, 1036.0, 1287.0, 1036.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [493.0, 2219.0, 1235.0, 2219.0, 1235.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1363.0, 2218.0, 1478.0, 2218.0, 1478.0, 2264.0, 1363.0, 2264.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2221.0, 320.0, 2221.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1337.0, 115.0, 1498.0, 115.0, 1498.0, 170.0, 1337.0, 170.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [217.0, 189.0, 284.0, 189.0, 284.0, 231.0, 217.0, 231.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [779.0, 189.0, 1487.0, 189.0, 1487.0, 226.0, 779.0, 226.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 1242.0, 535.0, 1242.0, 535.0, 1287.0, 152.0, 1287.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 190.0, 284.0, 190.0, 284.0, 230.0, 219.0, 230.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [199.0, 1421.0, 1257.0, 1421.0, 1257.0, 1456.0, 199.0, 1456.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [195.0, 1045.0, 489.0, 1045.0, 489.0, 1085.0, 195.0, 1085.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [625.0, 1045.0, 1496.0, 1045.0, 1496.0, 1085.0, 625.0, 1085.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 1088.0, 349.0, 1088.0, 349.0, 1132.0, 152.0, 1132.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [448.0, 1088.0, 638.0, 1088.0, 638.0, 1132.0, 448.0, 1132.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [733.0, 1088.0, 889.0, 1088.0, 889.0, 1132.0, 733.0, 1132.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [993.0, 1108.0, 1017.0, 1108.0, 1017.0, 1132.0, 993.0, 1132.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1070.0, 1099.5, 1100.0, 1099.5, 1100.0, 1110.5, 1070.0, 1110.5], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [193.0, 266.0, 806.0, 266.0, 806.0, 306.0, 193.0, 306.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [886.0, 266.0, 1205.0, 266.0, 1205.0, 306.0, 886.0, 306.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [199.0, 1421.0, 1257.0, 1421.0, 1257.0, 1456.0, 199.0, 1456.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [189.0, 306.0, 270.0, 306.0, 270.0, 349.0, 189.0, 349.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [369.0, 1170.0, 433.0, 1170.0, 433.0, 1208.0, 369.0, 1208.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [191.0, 1186.0, 298.0, 1186.0, 298.0, 1224.0, 191.0, 1224.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 16, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [172, 1034, 1500, 1034, 1500, 1382, 172, 1382], "score": 0.982, "html": "<table><tr><td>故际</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td>过温</td><td>MCU內部温度过温</td><td></td><td>1)过温关断判定成立 后即信报过温关 )输出关断。 2)</td><td>M内部温度 2)持续时间t&gt;20s</td><td>无</td></tr></table>"}, {"category_id": 5, "poly": [171, 1689, 1499, 1689, 1499, 2162, 171, 2162], "score": 0.981, "html": "<table><tr><td>故际</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td rowspan=\"2\">开路降</td><td>电机开路</td><td>电机运行电流I&lt; 80mA，持续t&gt;</td><td rowspan=\"2\">1)开路判定成立后水 阀停转，2s后尝试重 启；</td><td>1)电机运行电流 0.1A≤I≤0.5A，持续 500ms以上；</td><td>重启次数 机时间：</td></tr><tr><td>程）</td><td>周期内停止恢复。</td><td>到目标位置。</td><td></td></tr></table>"}, {"category_id": 5, "poly": [179, 260, 1495, 260, 1495, 802, 179, 802], "score": 0.976, "html": "<table><tr><td>位置传感器異常 （包含传感器供电 异常，传感器异</td><td>电机行过 电平信号不 转，且持续时间</td><td rowspan=\"2\">止运行，间隔2s后尝 试重次重启失败之</td><td>反感 2)阀体重启成功</td><td rowspan=\"2\">重启次数机时</td></tr><tr><td>阀芯角度低于量 程 （位置传感器反馈 的总脉冲数Naı小于 行程）</td><td>重启； 位置传感器反 馈脉冲数Nall</td><td>数605≤ Nau ≤ 3)直至重启合计10 698； 次，则在本工作周期 2)阀体恢复正常 内不在执行重启；同 后，EWV按照VCU指</td></tr></table>"}, {"category_id": 5, "poly": [249, 862, 1409, 862, 1409, 980, 249, 980], "score": 0.968, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 25</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [250, 1517, 1408, 1517, 1408, 1635, 250, 1635], "score": 0.963, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-26</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 2, "poly": [458, 2221, 1235, 2221, 1235, 2262, 458, 2262], "score": 0.921}, {"category_id": 2, "poly": [1368, 2224, 1472, 2224, 1472, 2258, 1368, 2258], "score": 0.921}, {"category_id": 1, "poly": [197, 1646, 1162, 1646, 1162, 1684, 197, 1684], "score": 0.914}, {"category_id": 0, "poly": [157, 817, 791, 817, 791, 858, 157, 858], "score": 0.895}, {"category_id": 2, "poly": [187, 2224, 317, 2224, 317, 2258, 187, 2258], "score": 0.862}, {"category_id": 0, "poly": [156, 1472, 615, 1472, 615, 1511, 156, 1511], "score": 0.845}, {"category_id": 2, "poly": [158, 113, 349, 113, 349, 242, 158, 242], "score": 0.782}, {"category_id": 1, "poly": [198, 994, 1043, 994, 1043, 1031, 198, 1031], "score": 0.684}, {"category_id": 1, "poly": [165, 1385, 1494, 1385, 1494, 1458, 165, 1458], "score": 0.54}, {"category_id": 2, "poly": [775, 187, 1489, 187, 1489, 231, 775, 231], "score": 0.499}, {"category_id": 1, "poly": [1338, 121, 1485, 121, 1485, 165, 1338, 165], "score": 0.495}, {"category_id": 7, "poly": [165, 1385, 1494, 1385, 1494, 1458, 165, 1458], "score": 0.323}, {"category_id": 6, "poly": [198, 994, 1043, 994, 1043, 1031, 198, 1031], "score": 0.223}, {"category_id": 13, "poly": [1049, 1827, 1215, 1827, 1215, 1860, 1049, 1860], "score": 0.79, "latex": "0 . 1 \\mathsf { A } \\leqslant 1 \\leqslant 0 . 5 \\mathsf { A }"}, {"category_id": 13, "poly": [1213, 1282, 1301, 1282, 1301, 1317, 1213, 1317], "score": 0.61, "latex": "t > 2 0 s"}, {"category_id": 13, "poly": [398, 669, 437, 669, 437, 698, 398, 698], "score": 0.52, "latex": "\\mathsf { N } _ { \\mathsf { a l l } }"}, {"category_id": 15, "poly": [459.0, 2219.0, 1235.0, 2219.0, 1235.0, 2265.0, 459.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1362.0, 2218.0, 1478.0, 2218.0, 1478.0, 2264.0, 1362.0, 2264.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [149.0, 809.0, 793.0, 809.0, 793.0, 867.0, 149.0, 867.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [183.0, 2220.0, 321.0, 2220.0, 321.0, 2268.0, 183.0, 2268.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 1470.0, 619.0, 1470.0, 619.0, 1514.0, 152.0, 1514.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [218.0, 188.0, 285.0, 188.0, 285.0, 232.0, 218.0, 232.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [778.0, 185.0, 1485.0, 185.0, 1485.0, 228.0, 778.0, 228.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [201.0, 1380.0, 1499.0, 1380.0, 1499.0, 1426.0, 201.0, 1426.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [160.0, 1416.0, 466.0, 1416.0, 466.0, 1459.0, 160.0, 1459.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [198.0, 992.0, 1047.0, 992.0, 1047.0, 1032.0, 198.0, 1032.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 1646.0, 1168.0, 1646.0, 1168.0, 1685.0, 197.0, 1685.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [198.0, 992.0, 1047.0, 992.0, 1047.0, 1032.0, 198.0, 1032.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [201.0, 1380.0, 1499.0, 1380.0, 1499.0, 1426.0, 201.0, 1426.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [160.0, 1416.0, 466.0, 1416.0, 466.0, 1459.0, 160.0, 1459.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1336.0, 115.0, 1494.0, 115.0, 1494.0, 175.0, 1336.0, 175.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 17, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [173, 1701, 1498, 1701, 1498, 2059, 173, 2059], "score": 0.982, "html": "<table><tr><td>故障名称</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td>Comm_Err</td><td>LIN通信故</td><td>BusOff</td><td>1) LIN Busoff判定成 立后上报过LIN通 信信障续时 间&gt;1s后，水阀运 行到安全位置。</td><td></td><td>无</td></tr></table>"}, {"category_id": 5, "poly": [173, 1155, 1499, 1155, 1499, 1462, 173, 1462], "score": 0.982, "html": "<table><tr><td>故障名称</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机製</td><td>重启策略</td></tr><tr><td>过温告</td><td>MCU内部温</td><td>MCU内温</td><td>1)过温警告判定成 行</td><td>)内温度≤125℃</td><td>无</td></tr></table>"}, {"category_id": 5, "poly": [171, 549, 1497, 549, 1497, 844, 171, 844], "score": 0.979, "html": "<table><tr><td>故障 名称</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td rowspan=\"2\">电压 故障</td><td>(0 ae)</td><td></td><td>满压B模</td><td>压落过持</td><td>无</td></tr><tr><td>(0)</td><td></td><td>满压B</td><td></td><td>无</td></tr></table>"}, {"category_id": 5, "poly": [250, 323, 1409, 323, 1409, 441, 250, 441], "score": 0.973, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 27</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [248, 1530, 1407, 1530, 1407, 1649, 248, 1649], "score": 0.973, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 29</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [250, 984, 1409, 984, 1409, 1104, 250, 1104], "score": 0.962, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC EWV 3WAY SOR 28</td><td>SAIC_EWV_3WAY_RFQ A1</td><td>Validation test</td></tr></table>"}, {"category_id": 1, "poly": [142, 452, 1454, 452, 1454, 543, 142, 543], "score": 0.944}, {"category_id": 2, "poly": [458, 2221, 1236, 2221, 1236, 2262, 458, 2262], "score": 0.93}, {"category_id": 2, "poly": [1368, 2224, 1472, 2224, 1472, 2258, 1368, 2258], "score": 0.929}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2259, 187, 2259], "score": 0.908}, {"category_id": 0, "poly": [154, 1480, 449, 1480, 449, 1521, 154, 1521], "score": 0.872}, {"category_id": 5, "poly": [154, 108, 1493, 108, 1493, 246, 154, 246], "score": 0.85, "html": "<table><tr><td>鹏翎 NGI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 0, "poly": [154, 936, 626, 936, 626, 976, 154, 976], "score": 0.831}, {"category_id": 1, "poly": [164, 845, 1495, 845, 1495, 919, 164, 919], "score": 0.831}, {"category_id": 1, "poly": [192, 1661, 1165, 1661, 1165, 1697, 192, 1697], "score": 0.821}, {"category_id": 0, "poly": [156, 274, 707, 274, 707, 316, 156, 316], "score": 0.772}, {"category_id": 6, "poly": [196, 1115, 1416, 1115, 1416, 1153, 196, 1153], "score": 0.642}, {"category_id": 2, "poly": [156, 110, 353, 110, 353, 244, 156, 244], "score": 0.64}, {"category_id": 1, "poly": [196, 1115, 1416, 1115, 1416, 1153, 196, 1153], "score": 0.25}, {"category_id": 7, "poly": [164, 845, 1495, 845, 1495, 919, 164, 919], "score": 0.127}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.38, "latex": "\\circledcirc"}, {"category_id": 15, "poly": [493.0, 2219.0, 1235.0, 2219.0, 1235.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1363.0, 2218.0, 1478.0, 2218.0, 1478.0, 2264.0, 1363.0, 2264.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2221.0, 320.0, 2221.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 1481.0, 449.0, 1481.0, 449.0, 1520.0, 152.0, 1520.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 930.0, 624.0, 930.0, 624.0, 985.0, 151.0, 985.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [149.0, 265.0, 709.0, 265.0, 709.0, 326.0, 149.0, 326.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [194.0, 1111.0, 1419.0, 1111.0, 1419.0, 1157.0, 194.0, 1157.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 190.0, 283.0, 190.0, 283.0, 231.0, 219.0, 231.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [202.0, 841.0, 1500.0, 841.0, 1500.0, 883.0, 202.0, 883.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [162.0, 879.0, 990.0, 879.0, 990.0, 917.0, 162.0, 917.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [199.0, 457.0, 1455.0, 457.0, 1455.0, 490.0, 199.0, 490.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 506.0, 210.0, 506.0, 210.0, 553.0, 151.0, 553.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [202.0, 841.0, 1500.0, 841.0, 1500.0, 883.0, 202.0, 883.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [162.0, 879.0, 990.0, 879.0, 990.0, 917.0, 162.0, 917.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 1660.0, 1167.0, 1660.0, 1167.0, 1700.0, 197.0, 1700.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [194.0, 1111.0, 1419.0, 1111.0, 1419.0, 1157.0, 194.0, 1157.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 18, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [182, 1021, 1470, 1021, 1470, 1856, 182, 1856], "score": 0.981, "html": "<table><tr><td>No.</td><td>数据名称</td><td>符号</td><td>数据格式</td><td>数据范围</td><td>备注</td></tr><tr><td>1</td><td>正常下电标志</td><td>SstateBit</td><td>int</td><td>0~1</td><td>1.此值在休眠时写入的顺序为最后， 即在其余参数写完之后再写入此 值；其中写入的参数为“1”。 2.上电/唤醒后，确认此参数之后需</td></tr><tr><td>2</td><td>水阀自标定Hall总 脉冲数</td><td>NHalAll</td><td>uint16</td><td>0~65,535</td><td>要立即写入参数为“0”。</td></tr><tr><td>3</td><td>水阀运行方向</td><td>Dair</td><td>int -</td><td>0~1</td><td></td></tr><tr><td>4</td><td>水阀运行总行程</td><td>nEWVAIl</td><td>uint32</td><td>0~4,294,967,295</td><td>记录水阀总计运行脉冲数</td></tr><tr><td>5</td><td>水阀故障标志位</td><td>FFaultBit</td><td>int</td><td>0~1</td><td>10：正常</td></tr><tr><td>6</td><td>水阀正向补偿量</td><td>ncw</td><td>uint8</td><td>0~255</td><td></td></tr><tr><td>7</td><td>水阀反向补偿量</td><td>nccw</td><td>uint8</td><td>0~255</td><td></td></tr><tr><td>8</td><td>水阀当前Hal脉冲</td><td>nHallcurrent</td><td>uint16</td><td>0~65,535</td><td></td></tr><tr><td>9</td><td>水阀休眠次数</td><td>nslepptime</td><td>uint8</td><td>0~255</td><td></td></tr></table>"}, {"category_id": 5, "poly": [249, 847, 1405, 847, 1405, 965, 249, 965], "score": 0.972, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 31</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [249, 1966, 1407, 1966, 1407, 2084, 249, 2084], "score": 0.97, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 32</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [246, 385, 1408, 385, 1408, 502, 246, 502], "score": 0.967, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>MERIT-EWV-3WAY-SOR- 30</td><td>MERIT_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 2, "poly": [1365, 2224, 1472, 2224, 1472, 2259, 1365, 2259], "score": 0.93}, {"category_id": 2, "poly": [458, 2221, 1235, 2221, 1235, 2262, 458, 2262], "score": 0.927}, {"category_id": 0, "poly": [156, 268, 628, 268, 628, 316, 156, 316], "score": 0.909}, {"category_id": 1, "poly": [201, 2095, 1239, 2095, 1239, 2137, 201, 2137], "score": 0.902}, {"category_id": 2, "poly": [187, 2223, 317, 2223, 317, 2259, 187, 2259], "score": 0.9}, {"category_id": 0, "poly": [155, 793, 728, 793, 728, 840, 155, 840], "score": 0.895}, {"category_id": 1, "poly": [192, 978, 1122, 978, 1122, 1015, 192, 1015], "score": 0.876}, {"category_id": 0, "poly": [153, 1918, 387, 1918, 387, 1964, 153, 1964], "score": 0.847}, {"category_id": 0, "poly": [157, 329, 728, 329, 728, 378, 157, 378], "score": 0.794}, {"category_id": 1, "poly": [198, 515, 774, 515, 774, 779, 198, 779], "score": 0.786}, {"category_id": 5, "poly": [155, 108, 1494, 108, 1494, 247, 155, 247], "score": 0.768, "html": "<table><tr><td>鹏翎</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 2, "poly": [156, 110, 350, 110, 350, 245, 156, 245], "score": 0.309}, {"category_id": 1, "poly": [157, 329, 728, 329, 728, 378, 157, 378], "score": 0.112}, {"category_id": 13, "poly": [917, 1633, 993, 1633, 993, 1662, 917, 1662], "score": 0.7, "latex": "0 \\sim 2 5 5"}, {"category_id": 13, "poly": [911, 1798, 996, 1798, 996, 1831, 911, 1831], "score": 0.7, "latex": "0 \\sim 2 5 5"}, {"category_id": 13, "poly": [912, 1569, 996, 1569, 996, 1601, 912, 1601], "score": 0.65, "latex": "0 \\sim 2 5 5"}, {"category_id": 13, "poly": [925, 1363, 981, 1363, 981, 1396, 925, 1396], "score": 0.58, "latex": "0 \\sim 1"}, {"category_id": 13, "poly": [892, 1709, 1015, 1709, 1015, 1743, 892, 1743], "score": 0.57, "latex": "\\sim 6 5 , 5 3 5"}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.48, "latex": "\\circledcirc"}, {"category_id": 13, "poly": [927, 1497, 981, 1497, 981, 1529, 927, 1529], "score": 0.47, "latex": "0 \\sim 1"}, {"category_id": 13, "poly": [554, 1558, 614, 1558, 614, 1608, 554, 1608], "score": 0.3, "latex": "n _ { C W }"}, {"category_id": 13, "poly": [848, 1428, 1059, 1428, 1059, 1462, 848, 1462], "score": 0.27, "latex": "0 ^ { \\sim } 4 , 2 9 4 , 9 6 7 , 2 9 5"}, {"category_id": 13, "poly": [552, 1349, 618, 1349, 618, 1402, 552, 1402], "score": 0.25, "latex": "D _ { d i r }"}, {"category_id": 15, "poly": [1359.0, 2216.0, 1480.0, 2216.0, 1480.0, 2266.0, 1359.0, 2266.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [493.0, 2219.0, 1235.0, 2219.0, 1235.0, 2265.0, 493.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [150.0, 266.0, 628.0, 266.0, 628.0, 319.0, 150.0, 319.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [184.0, 2221.0, 320.0, 2221.0, 320.0, 2267.0, 184.0, 2267.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [145.0, 787.0, 731.0, 787.0, 731.0, 845.0, 145.0, 845.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [146.0, 1917.0, 393.0, 1917.0, 393.0, 1967.0, 146.0, 1967.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 327.0, 729.0, 327.0, 729.0, 381.0, 152.0, 381.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [219.0, 190.0, 284.0, 190.0, 284.0, 231.0, 219.0, 231.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [198.0, 2097.0, 1242.0, 2097.0, 1242.0, 2135.0, 198.0, 2135.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 978.0, 1122.0, 978.0, 1122.0, 1015.0, 197.0, 1015.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [200.0, 515.0, 758.0, 515.0, 758.0, 552.0, 200.0, 552.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [200.0, 573.0, 497.0, 573.0, 497.0, 609.0, 200.0, 609.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [202.0, 625.0, 755.0, 625.0, 755.0, 664.0, 202.0, 664.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [200.0, 684.0, 759.0, 684.0, 759.0, 719.0, 200.0, 719.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [201.0, 740.0, 593.0, 740.0, 593.0, 774.0, 201.0, 774.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 327.0, 729.0, 327.0, 729.0, 381.0, 152.0, 381.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 19, "width": 1655, "height": 2338}}, {"layout_dets": [{"category_id": 5, "poly": [249, 385, 1408, 385, 1408, 503, 249, 503], "score": 0.973, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 33</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [247, 895, 1408, 895, 1408, 1010, 247, 1010], "score": 0.969, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 35</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 5, "poly": [250, 613, 1404, 613, 1404, 730, 250, 730], "score": 0.968, "html": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 34</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>"}, {"category_id": 1, "poly": [168, 1023, 1469, 1023, 1469, 1120, 168, 1120], "score": 0.951}, {"category_id": 2, "poly": [459, 2221, 1235, 2221, 1235, 2261, 459, 2261], "score": 0.929}, {"category_id": 2, "poly": [1365, 2225, 1471, 2225, 1471, 2257, 1365, 2257], "score": 0.916}, {"category_id": 1, "poly": [195, 516, 934, 516, 934, 554, 195, 554], "score": 0.916}, {"category_id": 2, "poly": [187, 2224, 317, 2224, 317, 2258, 187, 2258], "score": 0.909}, {"category_id": 0, "poly": [155, 846, 447, 846, 447, 892, 155, 892], "score": 0.892}, {"category_id": 0, "poly": [153, 336, 454, 336, 454, 377, 153, 377], "score": 0.843}, {"category_id": 1, "poly": [227, 260, 1082, 260, 1082, 318, 227, 318], "score": 0.72}, {"category_id": 2, "poly": [156, 111, 351, 111, 351, 244, 156, 244], "score": 0.694}, {"category_id": 1, "poly": [201, 1135, 1167, 1135, 1167, 1343, 201, 1343], "score": 0.644}, {"category_id": 1, "poly": [154, 565, 483, 565, 483, 610, 154, 610], "score": 0.504}, {"category_id": 5, "poly": [154, 108, 1494, 108, 1494, 245, 154, 245], "score": 0.502, "html": "<table><tr><td>鹏翎 INEI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>"}, {"category_id": 0, "poly": [154, 565, 483, 565, 483, 610, 154, 610], "score": 0.462}, {"category_id": 2, "poly": [198, 279, 226, 279, 226, 308, 198, 308], "score": 0.314}, {"category_id": 2, "poly": [197, 801, 228, 801, 228, 833, 197, 833], "score": 0.3}, {"category_id": 1, "poly": [200, 741, 1086, 741, 1086, 836, 200, 836], "score": 0.16}, {"category_id": 2, "poly": [154, 108, 1494, 108, 1494, 245, 154, 245], "score": 0.13}, {"category_id": 3, "poly": [197, 801, 228, 801, 228, 833, 197, 833], "score": 0.11}, {"category_id": 13, "poly": [789, 799, 847, 799, 847, 835, 789, 835], "score": 0.86, "latex": "50 \\%"}, {"category_id": 13, "poly": [789, 278, 848, 278, 848, 313, 789, 313], "score": 0.83, "latex": "50 \\%"}, {"category_id": 13, "poly": [611, 799, 697, 799, 697, 834, 611, 834], "score": 0.5, "latex": "= 0 \\times 7 0"}, {"category_id": 13, "poly": [458, 2223, 492, 2223, 492, 2258, 458, 2258], "score": 0.26, "latex": "\\circledcirc"}, {"category_id": 15, "poly": [493.0, 2221.0, 1234.0, 2221.0, 1234.0, 2263.0, 493.0, 2263.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1361.0, 2219.0, 1478.0, 2219.0, 1478.0, 2265.0, 1361.0, 2265.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [183.0, 2220.0, 321.0, 2220.0, 321.0, 2268.0, 183.0, 2268.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [151.0, 841.0, 451.0, 841.0, 451.0, 896.0, 151.0, 896.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [152.0, 334.0, 453.0, 334.0, 453.0, 383.0, 152.0, 383.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [218.0, 188.0, 285.0, 188.0, 285.0, 233.0, 218.0, 233.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [148.0, 563.0, 489.0, 563.0, 489.0, 615.0, 148.0, 615.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [198.0, 280.0, 227.0, 280.0, 227.0, 313.0, 198.0, 313.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [198.0, 801.0, 227.0, 801.0, 227.0, 833.0, 198.0, 833.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [1335.0, 111.0, 1499.0, 111.0, 1499.0, 172.0, 1335.0, 172.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [216.0, 188.0, 284.0, 188.0, 284.0, 230.0, 216.0, 230.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [777.0, 187.0, 1488.0, 187.0, 1488.0, 228.0, 777.0, 228.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [198.0, 801.0, 227.0, 801.0, 227.0, 833.0, 198.0, 833.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [198.0, 1021.0, 1363.0, 1021.0, 1363.0, 1064.0, 198.0, 1064.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [169.0, 1079.0, 1468.0, 1079.0, 1468.0, 1118.0, 169.0, 1118.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [196.0, 516.0, 936.0, 516.0, 936.0, 555.0, 196.0, 555.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [229.0, 271.0, 438.0, 271.0, 438.0, 315.0, 229.0, 315.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [443.0, 272.0, 697.0, 272.0, 697.0, 316.0, 443.0, 316.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [708.0, 273.0, 788.0, 273.0, 788.0, 314.0, 708.0, 314.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [849.0, 273.0, 1083.0, 273.0, 1083.0, 314.0, 849.0, 314.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [199.0, 1133.0, 1160.0, 1133.0, 1160.0, 1176.0, 199.0, 1176.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [201.0, 1191.0, 555.0, 1191.0, 555.0, 1228.0, 201.0, 1228.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [197.0, 1243.0, 716.0, 1243.0, 716.0, 1286.0, 197.0, 1286.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [200.0, 1302.0, 556.0, 1302.0, 556.0, 1340.0, 200.0, 1340.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [148.0, 563.0, 489.0, 563.0, 489.0, 615.0, 148.0, 615.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [200.0, 744.0, 1074.0, 744.0, 1074.0, 778.0, 200.0, 778.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [194.0, 799.0, 227.0, 799.0, 227.0, 833.0, 194.0, 833.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [228.0, 794.0, 291.0, 794.0, 291.0, 839.0, 228.0, 839.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [295.0, 797.0, 610.0, 797.0, 610.0, 836.0, 295.0, 836.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [698.0, 797.0, 788.0, 797.0, 788.0, 836.0, 698.0, 836.0], "score": 1.0, "text": ""}, {"category_id": 15, "poly": [848.0, 797.0, 1082.0, 797.0, 1082.0, 836.0, 848.0, 836.0], "score": 1.0, "text": ""}], "page_info": {"page_no": 20, "width": 1655, "height": 2338}}]
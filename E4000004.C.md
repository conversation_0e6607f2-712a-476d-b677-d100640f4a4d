<table><tr><td rowspan="2">鹏翎 INEI</td><td colspan="2">天津鹏翎集团股份有限公司</td></tr><tr><td>需求说明-SOR</td><td>No:PL/SH-E001-AA-2024</td></tr></table>

# EWV_3WAY_BLDC 需求说明

EWV_3WAY_BLDC Statement of Requirements

<table><tr><td>鹏翎 NGI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>

文档审批

更改记录  

<table><tr><td>审批信息</td><td>姓名</td><td>职位</td><td>日期</td></tr><tr><td>编制</td><td></td><td></td><td></td></tr><tr><td>审评</td><td></td><td></td><td></td></tr><tr><td>批注</td><td></td><td></td><td></td></tr></table>

<table><tr><td>版本</td><td>作者</td><td>日期</td><td>详细说明</td></tr><tr><td>A</td><td>Peter Yu</td><td>2025/1/10</td><td>初版释放。</td></tr><tr><td>B</td><td>Peter Yu</td><td>2025/3/25</td><td>1.根据客户端输入的LN信号矩阵表更新信号表；</td></tr><tr><td>C</td><td>Peter Yu</td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr></table>

# 目录

1. 概要....... ... 4  
1.1. 产品介绍 . .. 4  
1.2. 适用范围 . .. 4  
1.3. 缩写 ... ... 4  
1.4. 参考文件 .... .. 4  
2. 产品结构及电气接口 ....... ..... 4  
2.1. 产品组成 .. .. 4  
2.2. PIN 脚定义 . .. 6  
2.2.1. 供电端 VBAT ... ... 6  
2.2.2. 接地端 GND. .. 6  
2.2.3. 通信端 LIN ..... .. 6  
2.3. 执行器总成 .. 7  
2.3.1. 电气参数 . 7  
2.4. 硬件原理框图. 7  
2.5. 系统框图 . ......................................... . 8  
3. 功能模块 ... ............ 8  
3.1. 电源检测及电源模式. .......................................................................................... ... 8  
3.2. LIN 通信 . .... 9  
3.2.1. 水阀接收报文 . ... 9  
3.2.2. 水阀反馈报文 . ................................ . 10  
3.2.3. 诊断功能 . .. 10  
3.3. 自校正 ...............3.3.1. 自校正条件 3.3.2. 自校定策略 . .............................. ................ ... 12... 13 . 13  
3.4. 三通水阀运行. . 14  
3.5. 三通水阀运行模式. .. 14  
3.6. 位置检测 . .... 15  
3.7. 诊断功能 . ..... 16  
3.7.1. 电机诊断策略（Fault_Signal） .. 16  
3.7.2. 水阀电压故障（VoltageError） ....... .. 19  
3.7.3. 过温警告（TempWarn） .. 19  
3.7.4. LIN 通信故障 . .. 19  
3.8. E2PROM 数据存储/读写 .. .. 20  
3.9. 安全位置 .. ... 20  
3.9.1. 安全工况定义 . . 21  
3.10. 出厂位置定义..... ... 21

<table><tr><td>鹏翎 NG</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>

# 1. 概要

# 1.1. 产品介绍

此规范主要是针对 SAIC_BLDC_电子三通水阀 的结构设计、硬件架构（电气参数、硬件原理图框图）、软件部分（通信矩阵、诊断策略、控制逻辑）进行了定义。

其中此文件未经过 华翎智驭 允许，禁止传递给第三方企业或者个人。

# 1.2. 适用范围

此规范适用于 SAIC_BLDC-三通电子水阀项目 项目。

# 1.3. 缩写

表 1.3-1 名称缩写及解释  

<table><tr><td>序号</td><td>缩写</td><td>解释</td></tr><tr><td>1</td><td>EWV</td><td>Electronic Water Valve（电子水阀）</td></tr><tr><td>2</td><td>LIN</td><td>Local Interconnect Network（局域互联网络）</td></tr><tr><td>3</td><td>VBAT</td><td>Voltage of Battery（蓄电池电压）</td></tr><tr><td>4</td><td>SOR</td><td>Statement of Requirements（需求说明）</td></tr><tr><td>5</td><td>BLDC</td><td>Brushless Direct Current Motor（无刷直流电机）</td></tr></table>

# 1.4. 参考文件

表 1.4-1 参考文件  

<table><tr><td>序号</td><td>文件名</td><td>文件编号</td></tr><tr><td>1</td><td>《SAIC_TWV_3WAY_LIN_Matrix_A1-2025.03.10》</td><td></td></tr><tr><td>2</td><td></td><td></td></tr></table>

# 2. 产品结构及电气接口

# 2.1. 产品组成

三通水阀主要由阀体和执行器 2个模块组成，其中阀体总成的主要作用是切换水阀通道模式，执行器总成主要是驱动阀体按照整车 VCU指令运行。

![](images/1a93f9cf2f7ebadbe9140ccfa53d5f083b649af3b73567b708e621760978ae6e.jpg)  
Figure 2.1-1 三通水阀结构图

其中：阀体总成主要由 X-Ring、阀体、密封垫、阀芯、轴封、阀盖、螺丝等组成，其中阀芯在阀体内部按照特定的方向和位置运行，以达到特定水路的切换。执行器总成主要是给阀体总成提供运转动力，通过PCBA控制器电机按照特定方向和转速运转，并通过齿轮组传动，最终通过驱动齿轮带动阀体总成运转。

![](images/c9f73db46ad8cb070e9827009aeee86b24b530a71f0a88f3c3d9939450c6d152.jpg)  
Figure 2.1-2 3 通水阀爆炸图

<table><tr><td>鹏翎 NGI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>

# 2.2. PIN 脚定义

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-1</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

母端型号：2-1718645-1/TE

表 2.2-1 PIN 脚定义及参数  

<table><tr><td>PIN 脚</td><td>信号定义</td><td>额定电流 (A)</td><td>额定电压 (V)</td><td>最大电流 (A)</td><td>推荐线径 (mm²)</td><td>其他要求/备注</td></tr><tr><td>1</td><td>GND</td><td>0.5</td><td>0</td><td>1</td><td>0.5</td><td rowspan="3">接插件型号（公端）： 2-1564559-2 Code B</td></tr><tr><td>2</td><td>LIN</td><td>0.02</td><td>12</td><td>0.1</td><td>0.5</td></tr><tr><td>3</td><td>VBAT</td><td>0.5</td><td>12</td><td>1</td><td>0.5</td></tr><tr><td>4</td><td>N.C</td><td></td><td></td><td></td><td></td><td>1 2 3 4</td></tr></table>

# 2.2.1. 供电端 VBAT

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 2</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

此接口为EWV的供电端口，其内部的MCU，Motor 均由此接口进行供电。当电压处于 $U _ { \mathsf { P V R } }$ 允许范围内时，能保证 EWV 其不损坏；当电压处于 UNOM正常电压范围内时，EWV 才能保证实现全部功能。

表 2.2-2 VBAT 电气参数  

<table><tr><td>No.</td><td>Description</td><td>Symbol</td><td>Min.</td><td>Typ.</td><td>Max</td><td>Unit</td><td>Note</td></tr><tr><td>1</td><td>允许电压范围</td><td>UPVR</td><td>-14</td><td></td><td>20</td><td>V</td><td></td></tr><tr><td>2</td><td>正常电压范围</td><td>UNOM</td><td>9</td><td>12</td><td>16</td><td>V</td><td></td></tr><tr><td>3</td><td>供电电流</td><td>INOM</td><td></td><td></td><td>2</td><td>A</td><td></td></tr><tr><td>4</td><td>静态电流</td><td>lauiescent</td><td></td><td></td><td>100</td><td>μA</td><td>@13.5V</td></tr></table>

# 2.2.2. 接地端 GND

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 3</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

GND 端是 EWV 唯一的功率地和参考地。

# 2.2.3. 通信端 LIN

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-4</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

EWV 与 VCU 的通信方式为 LIN 通信。

<table><tr><td>鹏翎 NG</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>

表 2.2-3 LIN 电气参数  

<table><tr><td>No.</td><td>Description</td><td>Min.</td><td>Typ.</td><td>Max</td><td>Unit</td><td>Note</td></tr><tr><td>1</td><td>波特率 Bandwidth</td><td></td><td>19.2</td><td></td><td>Kbps</td><td></td></tr><tr><td>2</td><td>主从节点</td><td colspan="5">从节点模式</td></tr><tr><td>3</td><td>LIN电流损耗</td><td></td><td></td><td>100</td><td>mA</td><td></td></tr></table>

# 2.3. 执行器总成

# 2.3.1. 电气参数

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-5</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

三通水阀其主要功能是进行防冻液水路的切换，其内部集成位置传感器（开关 Hall）、直流无刷电机（BLDC）、PCBA。

表 2.3-1 水阀电气参数  

<table><tr><td>No.</td><td>Description</td><td>Symbol</td><td>Min.</td><td>Typ.</td><td>Max</td><td>Unit</td><td>Note</td></tr><tr><td>1</td><td>电机工作电压</td><td>UNOM</td><td>9</td><td>12</td><td>16</td><td>V</td><td></td></tr><tr><td>2</td><td rowspan="2">电机瞬时电流</td><td>IMAX</td><td></td><td></td><td>800</td><td>mA</td><td></td></tr><tr><td>3</td><td>持续时间</td><td></td><td></td><td>100</td><td>ms</td><td></td></tr><tr><td>4</td><td>电机额定功率</td><td>P</td><td></td><td>2.93</td><td>4.1</td><td>W</td><td></td></tr><tr><td>5</td><td rowspan="3">电机工作电流</td><td>IMAX</td><td></td><td></td><td>0.59</td><td>A</td><td></td></tr><tr><td>6</td><td>额定电流</td><td></td><td>0.318</td><td></td><td>A</td><td></td></tr><tr><td>7</td><td>堵转电流</td><td></td><td></td><td>1.098</td><td>A</td><td></td></tr><tr><td>8</td><td>电机类型</td><td>Mrype</td><td></td><td>BLDC</td><td></td><td></td><td></td></tr><tr><td>9</td><td>开关 Hall工作电压</td><td>VNom</td><td>3.0</td><td>5</td><td>5.5</td><td>V</td><td></td></tr><tr><td>10</td><td>开关 Hall 工作电流</td><td>INom</td><td>1.1</td><td>1.5</td><td>2.5</td><td>mA</td><td></td></tr></table>

# 2.4. 硬件原理框图

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-6</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

![](images/0f6b4f1448d9f5dc75b3c2b6741fc7fd662c72d63f97a258559829bfa3f31221.jpg)  
Figure 2.4-1 硬件原理框图

# 2.5. 系统框图

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 7</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

EVW 在整车端供电为 IG电，即在整车进入休眠之后水阀模块供电会被切断。

![](images/b11bef1e11c92d931492249532ca18a83dba41c5ed596a8ad5799e7653c3aa34.jpg)  
Figure 2.5-1 系统框图

# 3. 功能模块

3.1. 电源检测及电源模式

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-S0R-8</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

EWV模块保持对电源电压实时检测，并能在各种电压范围内自动进入相应的工作（电压）模式，工作（电压）模式定义如下：

➢电压A模式：即正常模式，EWV所有功能（包括负载控制、通讯、信号采集）均正常工作；  
$\gtrdot$ 电压B模式：此模式除了负载不能工作外，其余（通信与信号采集）模块均正常。

表 3.1-1 电源电压模式对应电压值定义  

<table><tr><td>No.</td><td>电源电压模式</td><td>低电压区间</td><td>高电压区间</td></tr><tr><td>1</td><td rowspan="2">电压A模式</td><td>Enter: ≥9.0V，&gt;500ms</td><td>Enter: ≤16.0V，&gt;500ms</td></tr><tr><td>2</td><td>Exit: ≤8.5V，&gt;500ms</td><td>Exit：≥16.5V，&gt;500ms</td></tr><tr><td>3</td><td rowspan="2">电压B模式</td><td>Enter：≤8.5V, &gt;500ms</td><td>Enter:≥16.5V, &gt;500ms</td></tr><tr><td>4</td><td>Exit：≥9.0V, &gt;500ms</td><td>Exit：≤16.0V，&gt;500ms</td></tr></table>

备注：

✓以上电压检测精度均定义为 $\cdot$ ；  
$\checkmark$ 以上电压参数中已包含回差（hysteresis）定义；  
$\cdot$ 在输出禁止条件消失 $5 0 0 \mathsf { m s }$ 后，应在 $\_$ 内重新使输出。

# 3.2. LIN 通信

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-9</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

三通水阀与整车 VCU 通信方式为 LIN，其主要包括：

➢ LIN 波特率为：19.2kbit/s；  
➢ LIN 总线接口执行标准参考 LIN2.1；  
➢三通水阀在整车端作为从节点；  
$\gtrdot$ LIN 通信相关功能描述、功能定义参考《SAIC_TWV_3WAY_LIN_Matrix_A1-2025.03.10》。

# 3.2.1. 水阀接收报文

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 10</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

整车在非休眠模式下，EWV 会接收到下列 LIN 报文：

表 3.2-1 EWV 接收报文  

<table><tr><td>No.</td><td>LIN Msg Name</td><td>ID(Raw)</td><td>Length(Byte)</td><td>Time</td><td>Description</td></tr><tr><td>1</td><td>Valve_Cmd</td><td>0x22</td><td>8</td><td>20ms</td><td rowspan="2">VCU请求水阀运行报文信息</td></tr><tr><td>2</td><td>MasterReq</td><td>0x3C</td><td>8</td><td>Event</td></tr></table>

表 3.2-2 EWV 接收报文信息  

<table><tr><td>ID</td><td>Signal Name</td><td>Signal Description</td><td>Start Bit</td><td>Bit Length</td><td>Signal Value Description</td></tr><tr><td>0x22</td><td>ECC_TWV_PosSet</td><td>水阀目标位置请求</td><td>24</td><td>8</td><td>PH=INT*0.4(%) nvalid:0xFB~OxFF</td></tr></table>

<table><tr><td>鹏翎</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>

<table><tr><td></td><td></td><td>水阀使能信号</td><td>32</td><td>1</td><td></td></tr><tr><td>0x3C</td><td>MasterReqMsg</td><td>诊断请求帧</td><td>0</td><td>64</td><td></td></tr></table>

# 3.2.2. 水阀反馈报文

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 11</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

整车在非休眠模式下，EWV 会接收到下列 LIN 报文：

表 3.2-3 EWV 反馈报文  

<table><tr><td>No.</td><td>LIN Msg Name</td><td>ID(Raw)</td><td>Length(Byte)</td><td>Time</td><td>Description</td></tr><tr><td>1</td><td>Valve_Rsp</td><td>0x24</td><td>8</td><td>20ms</td><td>EWV反馈水阀运行报文信息</td></tr><tr><td>2</td><td>SlaveResp</td><td>0x3D</td><td>8</td><td>Event</td><td></td></tr></table>

表 3.2-4 EWV 反馈报文信息  

<table><tr><td>ID</td><td>Signal Name</td><td>Signal Description</td><td>Start Bit</td><td>Bit Length</td><td> Signal Value Description</td></tr><tr><td>0x24</td><td>TWV_Resp_Error</td><td>LIN通信错误</td><td>0</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_OverVoltage Flt</td><td>水阀过压故障</td><td></td><td></td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_OverCurrent</td><td>水阀过流故障</td><td>2</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_OverTempFI t</td><td>水阀过温故障</td><td>3</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_UnderVolta geFlt</td><td>水阀欠压故障</td><td>4</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_Initsta</td><td>水阀始标</td><td>5</td><td>1</td><td>Ox1:Have nilized</td></tr><tr><td>TWV_StallFIlt</td><td>水阀堵转故障</td><td>6</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TwwMotorsta</td><td>水阀电机状态</td><td>7</td><td>1</td><td>0x0:Motor Running 0x1:Motor Stop</td></tr><tr><td>TWV_RealSta</td><td>水阀位置反馈</td><td>8</td><td>8</td><td>PH=INT*0.4(%) Invalid:0xFB~0xFF</td></tr><tr><td>0x3D</td><td>SlaveRespMsg</td><td>诊断反馈帧</td><td>0</td><td>64</td><td></td></tr></table>

# 3.2.3. 诊断功能

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 12</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

<table><tr><td>鹏翎 INGI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>

整车 VCU可以通过 $0 \times 3 0 < / 0 \times 3 0$ 报文读取 EWV软件/硬件版本信息以及控制水阀进行自校正功能。

# 3.2.3.1. 诊断报文协议定义

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 13</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

表 3.2-5VCU 请求报文信息定义  

<table><tr><td>ID</td><td>Master ReqBO</td><td>Master ReqB1</td><td>Master ReqB2</td><td>Master ReqB3</td><td>Master ReqB4</td><td>Master ReqB5</td><td>Master ReqB6</td><td>Master ReqB7</td></tr><tr><td>0x3C</td><td>NAD</td><td>PCI</td><td>SID</td><td>子服务</td><td>Suppier</td><td>Suplierr</td><td>Functinn</td><td>Functionn</td></tr></table>

表 3.2-6EWV 反馈报文信息定义  

<table><tr><td>ID</td><td>Slave ResB0</td><td>Slave ResB1</td><td>Slave ResB2</td><td>Slave ResB3</td><td>Slave ResB4</td><td>Slave ResB5</td><td>Slave ResB6</td><td>Slave ResB7</td></tr><tr><td>0x3D</td><td>NAD</td><td>PCI</td><td>RSID注1</td><td>DO</td><td>D1</td><td>D2 -</td><td>D3</td><td>D4</td></tr></table>

注 1： $R S 1 D = S 1 D + 0 \times 4 0$

表 3.2-7 参数列表  

<table><tr><td>序号</td><td>符号</td><td>定义参数</td><td>备注</td></tr><tr><td>1</td><td>NAD</td><td>0x32</td><td></td></tr><tr><td>2</td><td>PCI</td><td>0x06</td><td></td></tr><tr><td>3</td><td>SID</td><td>0xB2/0xB4</td><td></td></tr><tr><td>4</td><td>RSID</td><td>0xF2/0xF4</td><td></td></tr><tr><td>5</td><td>Supplier_ID</td><td>0x0000</td><td>暂定0000</td></tr><tr><td>6</td><td>Function_ID</td><td>0x0000</td><td>暂定0000</td></tr></table>

# 3.2.3.2. 软件/硬件版本号读取

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 14</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

三通水阀可以通过 $0 \times 3 0 < / 0 \times 3 0$ 服务帧和 $0 \times 8 4$ 诊断服务帧读取软件/硬件版本号。帧结构定义如下：

表 3.2-8 VCU 请求报文信息  

<table><tr><td>ID</td><td>Master ReqBO</td><td>Master ReqB1</td><td>Master ReqB2</td><td>Master ReqB3</td><td>Master ReqB4</td><td>Master ReqB5</td><td>Master ReqB6</td><td>Master ReqB7</td></tr><tr><td>0x3C</td><td>0x32</td><td>0x06</td><td>0xB4</td><td>0x00</td><td>0x00</td><td>0x00</td><td>0x00</td><td>0x00</td></tr></table>

<table><tr><td>鹏翎 LING</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>

表 3.2-9 EWV 反馈报文信息  

<table><tr><td>ID</td><td>Slave ResBO</td><td>Slave ResB1</td><td>Slave ResB2</td><td>Slave ResB3</td><td>Slave ResB4</td><td>Slave ResB5</td><td>Slave ResB6</td><td>Slave ResB7</td></tr><tr><td>0x3D</td><td>0x32</td><td>0x06</td><td>0xF4</td><td>软件 主版本</td><td>软件 次版本</td><td>硬件 主版本</td><td>硬件 次版本</td><td>OxFF</td></tr></table>

其中定义如下：

➢ 软件主版本：按照A,B,C,D 进行，对应的值参考 ASIIC值表示；如 $\mathsf { A } \mathsf { - } \mathsf { 4 1 }$ ， $\tt B \mathrm { \cdot } > 4 2$ ， $c \mathord { \mathrm { \sim } } 4 3$ 以此类推；

软件次版本：按照 0,1,2,3…9编码；

$\gtrdot$ 硬件主版本：按照A,B,C,D 进行，对应的值参考 ASIIC值表示；如 $\mathsf { A } \mathsf { - } \mathsf { 4 1 }$ ， $\mathsf { B } \mathrm { - } \mathsf { 4 } 2$ ，C->43 以此类推；

➢ 硬件次版本：按照 0,1,2,3…9 编码。

# 3.2.3.3. 水阀自标定控制

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 15</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

三通水阀可以通过 $0 \times 3 0 < / 0 \times 3 0$ 服务帧和0xB2 诊断服务帧、 $\mathtt { 0 } \mathtt { x } \mathtt { 3 0 }$ 子服务帧控制水阀进行自标定操作。帧结构定义如下：

表 3.2-10 VCU 请求报文信息  

<table><tr><td>ID</td><td>Master ReqBO</td><td>Master ReqB1</td><td>Master ReqB2</td><td>Master ReqB3</td><td>Master ReqB4</td><td>Master ReqB5</td><td>Master ReqB6</td><td>Master ReqB7</td></tr><tr><td>0x3C</td><td>0x32</td><td>0x06</td><td>0xB2</td><td>0x30</td><td>0x00</td><td>0x00</td><td>0x00</td><td>0x00</td></tr></table>

表 3.2-11 EWV 反馈报文信息  

<table><tr><td>ID</td><td>Slave ResBO</td><td>Slave ResB1</td><td>Slave ResB2</td><td>Slave ResB3</td><td>Slave ResB4</td><td>Slave ResB5</td><td>Slave ResB6</td><td>Slave ResB7</td></tr><tr><td>0x3D</td><td>0x32</td><td>0×06</td><td>0xF2</td><td>0x01注2</td><td>0x00</td><td>0x00</td><td>0x00</td><td>OxFF</td></tr></table>

注 2：

$\gtrdot$ 当值反馈为 $0 \times 1$ 时，表示水阀自标定成功；  
$\gtrdot$ 当值反馈为 $0 { \times } 0$ 时，表示水阀自标定失败。

# 3.3. 自校正

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 16</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

三通水阀内部电机采用的 BLDC 电机，由于其力矩较小，在长时间运转过程中可能存在丢步的风险。故增加自校正主要是为了匹配不同阀体的差异以及消除运行一段时间后而出现的误差。

<table><tr><td>鹏翎 NE</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>

# 3.3.1. 自校正条件

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 17</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

三通水阀在满足下列条件情况下才能进行自校正：

EWV重新上电或休眠唤醒后存在下列情况之一：  
➢ VCU 请求水阀自标定控制（0x3C / 0xB2 / 0x30）  
➢EWV上个周期异常掉电；  
➢ EWV 行程达到 100,000 步之后；  
➢EWV上个周期存在故障（过流、开路、堵转）且未恢复。

# 3.3.2. 自校定策略

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 18</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

a) 起始点位置定位

➢ 水阀控制电机按照正转方向进行运动；记录电机因为产生堵转而停止转动时所处的位置，并标记此处位置为起始位置，并记录此处 Hall脉冲为 0。

➢ 此阶段电机转速为 1000rpm（待定）运行。

b) 电机位置校正

➢ 电机反转方向按照 1000rpm 运行，记录电机因为产生堵转而停止转动时所处的位置，并标记此处位置为终止位置；并设置此时 Hall 累积反馈的总脉冲数为的 $N _ { a l l }$ 。

➢ 其中总脉冲数中包好了起始位置（ ${ \mathbf { } } \cdot N _ { 1 }$ ）和终止位置（ $( N _ { 2 }$ ）处的机械总误差步数 $N _ { E }$ ，电机实际旋转总步数 $N _ { n o m }$ ，电机旋转总角度 $\theta _ { n o m }$ ， 电机实际运行步数 $N _ { a c t }$ ，实际运行角度 $\theta _ { a c t }$ 计算公式如下：

$\begin{array} { r l } & { \theta _ { a c t } = \frac { N _ { a c t } } { N _ { n o m } } * \theta _ { n o m } } \\ & { N _ { n o m } = N _ { a l l } - N _ { E } } \\ & { N _ { E } = N _ { 1 } + N _ { 2 } } \end{array}$ 公式一公式二公式三

版本：

![](images/140fb04c16fe7b43a0e7fe40549dd8ff8e358a465828996b0a33ae8207986d81.jpg)  
Figure 3.3-1 水阀自校正示意图

c) 自校正时电机堵转判定条件

水阀自校正运行过程中需要实时监测电机运行电流，当监测到：

$\curlyeqsucc$ 电机运行电流 $I _ { a c t } { > } 0 . 2 5 \mathsf { A }$ ，持续时 $\mathsf { t } { > } 1 0 0 \mathsf { m s }$ ；  
➢ 连续检测到Hall信号未发生变化，且持续时间超过 100ms。  
满足以上条件则判定为自校正时堵转状态。

备注：以上具体参数需后期标定确认。

# 3.4. 三通水阀运行

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 19</td><td>SAIC_EWV_3WAY_ RFQ-A1</td><td>Validation test</td></tr></table>

三通水阀运行需要根据整车 VCU 指令，按照特定的方向及特定的行程运行；其要求如下：

最大角度运行时间 ${ < } 8 5$ ；  
集成水阀正常运行时：不同电压下按照相同的转速运行（ $2 0 ^ { \circ } / s ,$ ）：✓ 电源供电电压：9V\~16V。  
水阀自标定过程中暂定全速运行，后期以实际工况调整。

# 3.5. 三通水阀运行模式

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-20</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

三通水阀为比例水阀，其中：

$0 \%$ ： $_ { 2 \cdot > 1 }$ 连通， $3 \cdot > 1$ 截止；$\triangleright 0 \% \sim 1 0 0 \%$ ： $_ { 2 \cdot > 1 }$ ， $3 \cdot > 1$ 比例连通；$100 \%$ ： $_ { 2 \cdot > 1 }$ 截止， $3 \cdot > 1$ 连通。

表 3.5-1 三通水阀运行模式及角度对应关系  

<table><tr><td>序列</td><td>模式</td><td>三通水阀比率 (%)</td><td>角度范围 （°)</td><td>脈冲数</td><td>示意图</td><td>备注</td></tr></table>

版本：C

<table><tr><td>1</td><td>起始端限位</td><td>/</td><td>0°</td><td>0</td><td></td><td></td></tr><tr><td>2</td><td>/</td><td>0%</td><td>10°</td><td>74</td><td>B O</td><td></td></tr><tr><td>3</td><td></td><td>0%~100%</td><td>10°~80°</td><td></td><td>3* 2</td><td></td></tr><tr><td>4</td><td>/</td><td>100%</td><td>80° -</td><td>590</td><td>3 2</td><td></td></tr><tr><td>5</td><td>终止端限位</td><td></td><td>90°</td><td>664</td><td></td><td></td></tr></table>

# 3.6. 位置检测

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 21</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

水阀内部电机运行状态采用相对位置传感器（开关 Hall）来反馈阀芯的角度。

![](images/a1538b5d436d58decf75c6f0b0488a514525bf20074f941f8910a3fb8eaa66fa.jpg)  
Figure 3.6-1 Hall 硬件采集电路原理图

输出旋转角度 $\theta _ { a c t }$ 与脉冲数 $N _ { a c t }$ 的关系式如下：

<table><tr><td>鹏翎 NE</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>

$$
\begin{array} { r } { N _ { a c t } = \frac { \dot { \theta _ { a c t } ^ { \circ } } } { 3 6 0 ^ { \circ } } * \frac { 1 } { K } * n * 2 } \end{array}
$$

公式四

其中： $\theta _ { a c t }$ 代表阀芯旋转角度，360 代表旋转一圈为 $3 6 0 ^ { \circ }$ ，K 为齿轮的减速比，n 为磁铁极对数，2 表示为磁铁磁极变化 1次时 Hall高低变化2 次。

# 3.7. 诊断功能

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-22</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

表 3.7-1 水阀故障诊断列表  

<table><tr><td>信号定义</td><td>故障类型</td><td>故障优先级</td><td>故障判定前置条件</td><td>备注</td></tr><tr><td rowspan="7">电机诊断 Fault_Signal</td><td>过流故障</td><td>1</td><td>全程检测</td><td></td></tr><tr><td rowspan="3">堵转故障</td><td>2（电机堵转）</td><td>全程检测</td><td></td></tr><tr><td>2（阀芯角度低于量程）</td><td>自校正检测</td><td></td></tr><tr><td>2 （传感器异常）</td><td>全程检测 - -</td><td></td></tr><tr><td>过温故障</td><td>3</td><td>全程检测 -</td><td></td></tr><tr><td rowspan="2">开路故障</td><td>4（电机开路）</td><td>全程检测</td><td></td></tr><tr><td>4（阀芯角度超量程）</td><td>自校正检测</td><td></td></tr><tr><td rowspan="2">VoltageErr</td><td>过压故障</td><td>-</td><td>全程检测</td><td></td></tr><tr><td>欠压故障</td><td></td><td>全程检测</td><td></td></tr><tr><td>RespError</td><td>LIN通信故障</td><td></td><td>全程检测</td><td></td></tr></table>

# 3.7.1. 电机诊断策略（Fault_Signal）

# 3.7.1.1. 过流故障（OverCurrent）

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-23</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

线圈短路故障是由于电机 PIN针短接在一起或者电机 PIN对电源/对地短路而引起的故障。

<table><tr><td>故脉</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复</td><td>重启策略</td></tr><tr><td>线图</td><td>全程检测</td><td>MOs管降</td><td>1)线圈短路故障判定成立后水 阀停转，2s后尝试重启； 2) 第5次重启失败之后，EWV 反线圈短路故障，且再 3) 直至重启合计10次，则在本 工作周期内不在执行重启，同 时不响应VCU的位置请求；同 时位置反馈信息为当前位置。</td><td>VDs&lt;1.8V</td><td>重启次数及时间： 每次问满， 停止重启。</td></tr></table>

备注：软件部分需求设置 HBSTATUS(0x06)分别开启 $\mathsf { A } / \mathsf { B } / \mathsf { C }$ 通道 half bridge 过流保护。

其中：

![](images/933cd3dd5f4530dc96fb44597a604bb9ae9c02be843ad3dc8672ff84b6b58a16.jpg)

当检测到通过 MOS 管的 $\mathsf { V } _ { \mathsf { D } \mathsf { S } } { \mathsf { - } } 1 . 8 \mathsf { V } \mathsf { S }$ 时，MCU 会关断 MOS 输出，以达到保护功率器件不被损坏；此时 MOS的最大保护电流 $I _ { M O S M A X }$ 与最小保护电流 $. I _ { M O S M I N }$ 关系式如下：

$$
\begin{array} { r } { I _ { M O S M I N } = \frac { V _ { R E F } } { G _ { A I N ^ { * R } M O S M A X } } = \frac { 1 . 8 } { 1 * 1 . 1 5 } = 1 . 5 7 A } \end{array}
$$

# 3.7.1.2. 堵转故障（Stall）

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 24</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

堵转故障是定义阀体在运行过程中由于阀体堵转、位置传感器异常引起的故障。

<table><tr><td>故障</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td>堵转 故障</td><td>电机堵转</td><td>电机运行电流I</td><td>1)堵转判定成立后 水阀停止运行，2s后 尝试重启。 2) 第5次重启失败之 后，EWV开始上报堵 转故障，并再次执行 重启； 3)直至重启合计10 次，则在本工作周期 内不在行向自</td><td>1)电机运行电流 0.1A≤I≤0.4A，持 续&gt; 0ms 以 2)阀体重启成功 后,水阀开始进 行到0°）；运行 到0°后按照VCU 指令运行到目标</td><td>重启次数机时 间： 每次重启间隔 2s；重启10次 无法恢复，则 停止重启。</td></tr></table>

版本：C

<table><tr><td>位置传感器異常 （包含传感器供电 异常，传感器异</td><td>电机行过 电平信号不 转，且持续时间</td><td rowspan="2">止运行，间隔2s后尝 试重次重启失败之</td><td>反感 2)阀体重启成功</td><td rowspan="2">重启次数机时</td></tr><tr><td>阀芯角度低于量 程 （位置传感器反馈 的总脉冲数Naı小于 行程）</td><td>重启； 位置传感器反 馈脉冲数Nall</td><td>数605≤ Nau ≤ 3)直至重启合计10 698； 次，则在本工作周期 2)阀体恢复正常 内不在执行重启；同 后，EWV按照VCU指</td></tr></table>

# 3.7.1.3. 过温故障（OverTempShutdown）

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 25</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

过温故障是定义阀体驱动芯片温度内部超过阀值而引起的故障。

<table><tr><td>故际</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td>过温</td><td>MCU內部温度过温</td><td></td><td>1)过温关断判定成立 后即信报过温关 )输出关断。 2)</td><td>M内部温度 2)持续时间t&gt;20s</td><td>无</td></tr></table>

备注：过温关断故障恢复后 EWV 需立即按照 VCU 指令从关断位置运行到最新的目标位置，若指令位置与当前位置一致则保持静止。

# 3.7.1.4. 开路故障（Coil_Open）

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-26</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

开路故障是定义阀体在运行过程中由于阀体断齿、电机开路引起的故障。

<table><tr><td>故际</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td rowspan="2">开路降</td><td>电机开路</td><td>电机运行电流I&lt; 80mA，持续t&gt;</td><td rowspan="2">1)开路判定成立后水 阀停转，2s后尝试重 启；</td><td>1)电机运行电流 0.1A≤I≤0.5A，持续 500ms以上；</td><td>重启次数 机时间：</td></tr><tr><td>程）</td><td>周期内停止恢复。</td><td>到目标位置。</td><td></td></tr></table>

<table><tr><td>鹏翎 NGI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>

# 3.7.2. 水阀电压故障（VoltageError）

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 27</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

水阀电压故障是定义由于整车供电异常，导致水阀供电端电压高于其正常电压范围而定义的故障。

<table><tr><td>故障 名称</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td rowspan="2">电压 故障</td><td>(0 ae)</td><td></td><td>满压B模</td><td>压落过持</td><td>无</td></tr><tr><td>(0)</td><td></td><td>满压B</td><td></td><td>无</td></tr></table>

备注：EWV 在发生电压故障，阀体停止在当前位置，在电压故障恢复后 EWV 需立即按照 VCU指令从当前位置运行到最新的目标位置，若指令位置与当前位置一致则保持静止。

# 3.7.3. 过温警告（TempWarn）

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC EWV 3WAY SOR 28</td><td>SAIC_EWV_3WAY_RFQ A1</td><td>Validation test</td></tr></table>

过温警告故障是定义由于 PCBA 发热而导致 MCU 发热严重，为了保护 MCU 而定义的故障。  

<table><tr><td>故障名称</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机製</td><td>重启策略</td></tr><tr><td>过温告</td><td>MCU内部温</td><td>MCU内温</td><td>1)过温警告判定成 行</td><td>)内温度≤125℃</td><td>无</td></tr></table>

# 3.7.4. LIN 通信故障

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 29</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

LIN 通信故障是指 LIN 总线出现断开，Busoff，短路等情况而定义的故障。

<table><tr><td>故障名称</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td>Comm_Err</td><td>LIN通信故</td><td>BusOff</td><td>1) LIN Busoff判定成 立后上报过LIN通 信信障续时 间&gt;1s后，水阀运 行到安全位置。</td><td></td><td>无</td></tr></table>

<table><tr><td>鹏翎</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>

# 3.8. E2PROM 数据存储/读写

# 3.8.1. E2PROM 数据存储/读写条件

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>MERIT-EWV-3WAY-SOR- 30</td><td>MERIT_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

E2PROM 在下列情况下需要进行数据存储；  
➢ EWV进入休眠模式；  
➢ EWV异常掉电(当电源电压＜10V时）。  
E2PROM 在下列情况下需要进行数据读写；  
➢ EWV上电或者休眠唤醒后；

# 3.8.2. E2PROM 数据存储/读写策略

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 31</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

E2PROM主要是用于存储掉电后需要记忆的数据，主要包括下列数据：

<table><tr><td>No.</td><td>数据名称</td><td>符号</td><td>数据格式</td><td>数据范围</td><td>备注</td></tr><tr><td>1</td><td>正常下电标志</td><td>SstateBit</td><td>int</td><td>0~1</td><td>1.此值在休眠时写入的顺序为最后， 即在其余参数写完之后再写入此 值；其中写入的参数为“1”。 2.上电/唤醒后，确认此参数之后需</td></tr><tr><td>2</td><td>水阀自标定Hall总 脉冲数</td><td>NHalAll</td><td>uint16</td><td>0~65,535</td><td>要立即写入参数为“0”。</td></tr><tr><td>3</td><td>水阀运行方向</td><td>Dair</td><td>int -</td><td>0~1</td><td></td></tr><tr><td>4</td><td>水阀运行总行程</td><td>nEWVAIl</td><td>uint32</td><td>0~4,294,967,295</td><td>记录水阀总计运行脉冲数</td></tr><tr><td>5</td><td>水阀故障标志位</td><td>FFaultBit</td><td>int</td><td>0~1</td><td>10：正常</td></tr><tr><td>6</td><td>水阀正向补偿量</td><td>ncw</td><td>uint8</td><td>0~255</td><td></td></tr><tr><td>7</td><td>水阀反向补偿量</td><td>nccw</td><td>uint8</td><td>0~255</td><td></td></tr><tr><td>8</td><td>水阀当前Hal脉冲</td><td>nHallcurrent</td><td>uint16</td><td>0~65,535</td><td></td></tr><tr><td>9</td><td>水阀休眠次数</td><td>nslepptime</td><td>uint8</td><td>0~255</td><td></td></tr></table>

# 3.9. 安全位置

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 32</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

安全位置是定义当EWV出现异常之后，为了保证整车安全行驶而定义的位置。

安全位置定义： ReqMovePos =0x7D （开度 $50 \%$ ：1&2&3 互通）。

# 3.9.1. 安全工况定义

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 33</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

➢ EWV与 VCU通信故障（包括通信故障、通信丢失）；

3.10. 出厂位置定义

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 34</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

出厂位置是定义为了满足整车防冻液的加注和排气而定义的位置。出厂 位置定义：ReqMovePos $= 0 \times 7 0$ （开度 $50 \%$ ：1&2&3 互通）。

# 3.11. 休眠与唤醒

<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 35</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>

为了降低功耗并确保系统在需要时快速恢复正常工作状态，有两种基本工作模式：运行（ACTIVE）、睡眠（SLEEP）。其中：以下两个条件有一个成立，立即进入睡眠，静态电流≤100uA。

➢ 接收到 LIN 睡眠指令(ID:0x3C) 0x00 0xFF 0xFF 0xFF 0xFF 0xFF 0xFF 0xFF；  
➢ 总线 4S 空闲条件成立。  
满足下面条件，EWV可从睡眠中唤醒。  
➢ 通讯总线上有任意活动。
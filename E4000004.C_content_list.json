[{"type": "table", "img_path": "images/23cbee45ff3bb9292a2a21535d1e4b3eb288e95c904c8e8515839d57c340a627.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td rowspan=\"2\">鹏翎 INEI</td><td colspan=\"2\">天津鹏翎集团股份有限公司</td></tr><tr><td>需求说明-SOR</td><td>No:PL/SH-E001-AA-2024</td></tr></table>", "page_idx": 0}, {"type": "text", "text": "EWV_3WAY_BLDC 需求说明", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "EWV_3WAY_BLDC Statement of Requirements ", "page_idx": 0}, {"type": "table", "img_path": "images/d49b7d04905f6e5e5593bc2566390e6ad8f6ed583ec8d109c3e62aa89650bb13.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>鹏翎 NGI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>", "page_idx": 1}, {"type": "text", "text": "文档审批", "page_idx": 1}, {"type": "table", "img_path": "images/f63a80cef2368e0bfd38735ce17cbef7c252fd8dc05681b56e04d4bdca6cdd5f.jpg", "table_caption": ["更改记录"], "table_footnote": [], "table_body": "<table><tr><td>审批信息</td><td>姓名</td><td>职位</td><td>日期</td></tr><tr><td>编制</td><td></td><td></td><td></td></tr><tr><td>审评</td><td></td><td></td><td></td></tr><tr><td>批注</td><td></td><td></td><td></td></tr></table>", "page_idx": 1}, {"type": "table", "img_path": "images/2cc494a401a9410ad2361544fd8c0deb42d65216ca8125b1280d137fa71014c8.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>版本</td><td>作者</td><td>日期</td><td>详细说明</td></tr><tr><td>A</td><td>Peter <PERSON></td><td>2025/1/10</td><td>初版释放。</td></tr><tr><td>B</td><td>Peter <PERSON></td><td>2025/3/25</td><td>1.根据客户端输入的LN信号矩阵表更新信号表；</td></tr><tr><td>C</td><td>Peter <PERSON></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr></table>", "page_idx": 1}, {"type": "text", "text": "目录", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "1. 概要....... ... 4  \n1.1. 产品介绍 . .. 4  \n1.2. 适用范围 . .. 4  \n1.3. 缩写 ... ... 4  \n1.4. 参考文件 .... .. 4  \n2. 产品结构及电气接口 ....... ..... 4  \n2.1. 产品组成 .. .. 4  \n2.2. PIN 脚定义 . .. 6  \n2.2.1. 供电端 VBAT ... ... 6  \n2.2.2. 接地端 GND. .. 6  \n2.2.3. 通信端 LIN ..... .. 6  \n2.3. 执行器总成 .. 7  \n2.3.1. 电气参数 . 7  \n2.4. 硬件原理框图. 7  \n2.5. 系统框图 . ......................................... . 8  \n3. 功能模块 ... ............ 8  \n3.1. 电源检测及电源模式. .......................................................................................... ... 8  \n3.2. LIN 通信 . .... 9  \n3.2.1. 水阀接收报文 . ... 9  \n3.2.2. 水阀反馈报文 . ................................ . 10  \n3.2.3. 诊断功能 . .. 10  \n3.3. 自校正 ...............3.3.1. 自校正条件 3.3.2. 自校定策略 . .............................. ................ ... 12... 13 . 13  \n3.4. 三通水阀运行. . 14  \n3.5. 三通水阀运行模式. .. 14  \n3.6. 位置检测 . .... 15  \n3.7. 诊断功能 . ..... 16  \n3.7.1. 电机诊断策略（Fault_Signal） .. 16  \n3.7.2. 水阀电压故障（VoltageError） ....... .. 19  \n3.7.3. 过温警告（TempWarn） .. 19  \n3.7.4. LIN 通信故障 . .. 19  \n3.8. E2PROM 数据存储/读写 .. .. 20  \n3.9. 安全位置 .. ... 20  \n3.9.1. 安全工况定义 . . 21  \n3.10. 出厂位置定义..... ... 21", "page_idx": 2}, {"type": "table", "img_path": "images/404093acf80d3f770188e79e9db5242f5e83e5d4a55b9da69b576952dca2e1c1.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>鹏翎 NG</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>", "page_idx": 3}, {"type": "text", "text": "1. 概要 ", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "1.1. 产品介绍 ", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "此规范主要是针对 SAIC_BLDC_电子三通水阀 的结构设计、硬件架构（电气参数、硬件原理图框图）、软件部分（通信矩阵、诊断策略、控制逻辑）进行了定义。", "page_idx": 3}, {"type": "text", "text": "其中此文件未经过 华翎智驭 允许，禁止传递给第三方企业或者个人。", "page_idx": 3}, {"type": "text", "text": "1.2. 适用范围 ", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "此规范适用于 SAIC_BLDC-三通电子水阀项目 项目。", "page_idx": 3}, {"type": "text", "text": "1.3. 缩写 ", "text_level": 1, "page_idx": 3}, {"type": "table", "img_path": "images/c3aca94c2ae4b9cbdbe56588fa920136d675e1333a28c8b13a97bb184140e93c.jpg", "table_caption": ["表 1.3-1 名称缩写及解释"], "table_footnote": [], "table_body": "<table><tr><td>序号</td><td>缩写</td><td>解释</td></tr><tr><td>1</td><td>EWV</td><td>Electronic Water Valve（电子水阀）</td></tr><tr><td>2</td><td>LIN</td><td>Local Interconnect Network（局域互联网络）</td></tr><tr><td>3</td><td>VBAT</td><td>Voltage of Battery（蓄电池电压）</td></tr><tr><td>4</td><td>SOR</td><td>Statement of Requirements（需求说明）</td></tr><tr><td>5</td><td>BLDC</td><td>Brushless Direct Current Motor（无刷直流电机）</td></tr></table>", "page_idx": 3}, {"type": "text", "text": "1.4. 参考文件", "text_level": 1, "page_idx": 3}, {"type": "table", "img_path": "images/08bea72af940e290cc8079236dd1177d6d31a5223b99d3ac0641472000ccbbaa.jpg", "table_caption": ["表 1.4-1 参考文件"], "table_footnote": [], "table_body": "<table><tr><td>序号</td><td>文件名</td><td>文件编号</td></tr><tr><td>1</td><td>《SAIC_TWV_3WAY_LIN_Matrix_A1-2025.03.10》</td><td></td></tr><tr><td>2</td><td></td><td></td></tr></table>", "page_idx": 3}, {"type": "text", "text": "2. 产品结构及电气接口", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "2.1. 产品组成", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "三通水阀主要由阀体和执行器 2个模块组成，其中阀体总成的主要作用是切换水阀通道模式，执行器总成主要是驱动阀体按照整车 VCU指令运行。", "page_idx": 3}, {"type": "image", "img_path": "images/1a93f9cf2f7ebadbe9140ccfa53d5f083b649af3b73567b708e621760978ae6e.jpg", "image_caption": ["Figure 2.1-1 三通水阀结构图"], "image_footnote": [], "page_idx": 4}, {"type": "text", "text": "其中：阀体总成主要由 X-Ring、阀体、密封垫、阀芯、轴封、阀盖、螺丝等组成，其中阀芯在阀体内部按照特定的方向和位置运行，以达到特定水路的切换。执行器总成主要是给阀体总成提供运转动力，通过PCBA控制器电机按照特定方向和转速运转，并通过齿轮组传动，最终通过驱动齿轮带动阀体总成运转。", "page_idx": 4}, {"type": "image", "img_path": "images/c9f73db46ad8cb070e9827009aeee86b24b530a71f0a88f3c3d9939450c6d152.jpg", "image_caption": ["Figure 2.1-2 3 通水阀爆炸图"], "image_footnote": [], "page_idx": 4}, {"type": "table", "img_path": "images/c50bccc20e6e53dec5abf2a1507aa4c70490f511867baae2393d2fd0112e7af1.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>鹏翎 NGI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>", "page_idx": 5}, {"type": "text", "text": "2.2. P<PERSON> 脚定义", "text_level": 1, "page_idx": 5}, {"type": "table", "img_path": "images/64eac83b43b704620f73217fe6e86ff58a5351d7c65083062e6b92cd0ed52f90.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-1</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 5}, {"type": "text", "text": "母端型号：2-1718645-1/TE", "page_idx": 5}, {"type": "table", "img_path": "images/69e9131500f3585e311a217fac6849c6fc840340fc29936761e2f2bb12e6b49c.jpg", "table_caption": ["表 2.2-1 PIN 脚定义及参数"], "table_footnote": [], "table_body": "<table><tr><td>PIN 脚</td><td>信号定义</td><td>额定电流 (A)</td><td>额定电压 (V)</td><td>最大电流 (A)</td><td>推荐线径 (mm²)</td><td>其他要求/备注</td></tr><tr><td>1</td><td>GND</td><td>0.5</td><td>0</td><td>1</td><td>0.5</td><td rowspan=\"3\">接插件型号（公端）： 2-1564559-2 Code B</td></tr><tr><td>2</td><td>LIN</td><td>0.02</td><td>12</td><td>0.1</td><td>0.5</td></tr><tr><td>3</td><td>VBAT</td><td>0.5</td><td>12</td><td>1</td><td>0.5</td></tr><tr><td>4</td><td>N.C</td><td></td><td></td><td></td><td></td><td>1 2 3 4</td></tr></table>", "page_idx": 5}, {"type": "text", "text": "2.2.1. 供电端 VBAT ", "text_level": 1, "page_idx": 5}, {"type": "table", "img_path": "images/e5128747def6c9f2a3d9c5cb14add17655cf93748351e26b5019e0259b59a2cb.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 2</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 5}, {"type": "text", "text": "此接口为EWV的供电端口，其内部的MCU，Motor 均由此接口进行供电。当电压处于 $U _ { \\mathsf { P V R } }$ 允许范围内时，能保证 EWV 其不损坏；当电压处于 UNOM正常电压范围内时，EWV 才能保证实现全部功能。", "page_idx": 5}, {"type": "table", "img_path": "images/b54d022c7c2549d0ccc19c52c657a7c3a3d51c232818ba0ccbcf13753a837839.jpg", "table_caption": ["表 2.2-2 VBAT 电气参数"], "table_footnote": [], "table_body": "<table><tr><td>No.</td><td>Description</td><td>Symbol</td><td>Min.</td><td>Typ.</td><td>Max</td><td>Unit</td><td>Note</td></tr><tr><td>1</td><td>允许电压范围</td><td>UPVR</td><td>-14</td><td></td><td>20</td><td>V</td><td></td></tr><tr><td>2</td><td>正常电压范围</td><td>UNOM</td><td>9</td><td>12</td><td>16</td><td>V</td><td></td></tr><tr><td>3</td><td>供电电流</td><td>INOM</td><td></td><td></td><td>2</td><td>A</td><td></td></tr><tr><td>4</td><td>静态电流</td><td>lauiescent</td><td></td><td></td><td>100</td><td>μA</td><td>@13.5V</td></tr></table>", "page_idx": 5}, {"type": "text", "text": "2.2.2. 接地端 GND ", "text_level": 1, "page_idx": 5}, {"type": "table", "img_path": "images/b2119ee8cf9e92076de1d648114e0a0de253658dad032d07e36ad292a5f3a6f7.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 3</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 5}, {"type": "text", "text": "GND 端是 EWV 唯一的功率地和参考地。", "page_idx": 5}, {"type": "text", "text": "2.2.3. 通信端 LIN", "text_level": 1, "page_idx": 5}, {"type": "table", "img_path": "images/577f44960fe2816fea9a4b62137b58f0fe5e5ee0c57e485ecbeb16ca3246b0ba.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-4</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 5}, {"type": "text", "text": "EWV 与 VCU 的通信方式为 LIN 通信。", "page_idx": 5}, {"type": "table", "img_path": "images/62802d0ad8151b845eb84c070a01d26fc551324b2a516b294d6b2a709e55f929.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>鹏翎 NG</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>", "page_idx": 6}, {"type": "table", "img_path": "images/e71e6b8a751d50bb8bdf385111f66f268b81fba8e5618a8ebd678df3da20b286.jpg", "table_caption": ["表 2.2-3 LIN 电气参数"], "table_footnote": [], "table_body": "<table><tr><td>No.</td><td>Description</td><td>Min.</td><td>Typ.</td><td>Max</td><td>Unit</td><td>Note</td></tr><tr><td>1</td><td>波特率 Bandwidth</td><td></td><td>19.2</td><td></td><td>Kbps</td><td></td></tr><tr><td>2</td><td>主从节点</td><td colspan=\"5\">从节点模式</td></tr><tr><td>3</td><td>LIN电流损耗</td><td></td><td></td><td>100</td><td>mA</td><td></td></tr></table>", "page_idx": 6}, {"type": "text", "text": "2.3. 执行器总成", "text_level": 1, "page_idx": 6}, {"type": "text", "text": "2.3.1. 电气参数", "text_level": 1, "page_idx": 6}, {"type": "table", "img_path": "images/ed579210e2699489eeb10e7dd8ba6968e35d6d458f5ba575578189374e382be8.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-5</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 6}, {"type": "text", "text": "三通水阀其主要功能是进行防冻液水路的切换，其内部集成位置传感器（开关 Hall）、直流无刷电机（BLDC）、PCBA。", "page_idx": 6}, {"type": "table", "img_path": "images/7c437909988633efd77cff739ad71b875d938d9f5edd38f694bec628eba2ff84.jpg", "table_caption": ["表 2.3-1 水阀电气参数"], "table_footnote": [], "table_body": "<table><tr><td>No.</td><td>Description</td><td>Symbol</td><td>Min.</td><td>Typ.</td><td>Max</td><td>Unit</td><td>Note</td></tr><tr><td>1</td><td>电机工作电压</td><td>UNOM</td><td>9</td><td>12</td><td>16</td><td>V</td><td></td></tr><tr><td>2</td><td rowspan=\"2\">电机瞬时电流</td><td>IMAX</td><td></td><td></td><td>800</td><td>mA</td><td></td></tr><tr><td>3</td><td>持续时间</td><td></td><td></td><td>100</td><td>ms</td><td></td></tr><tr><td>4</td><td>电机额定功率</td><td>P</td><td></td><td>2.93</td><td>4.1</td><td>W</td><td></td></tr><tr><td>5</td><td rowspan=\"3\">电机工作电流</td><td>IMAX</td><td></td><td></td><td>0.59</td><td>A</td><td></td></tr><tr><td>6</td><td>额定电流</td><td></td><td>0.318</td><td></td><td>A</td><td></td></tr><tr><td>7</td><td>堵转电流</td><td></td><td></td><td>1.098</td><td>A</td><td></td></tr><tr><td>8</td><td>电机类型</td><td>Mrype</td><td></td><td>BLDC</td><td></td><td></td><td></td></tr><tr><td>9</td><td>开关 Hall工作电压</td><td>VNom</td><td>3.0</td><td>5</td><td>5.5</td><td>V</td><td></td></tr><tr><td>10</td><td>开关 Hall 工作电流</td><td>INom</td><td>1.1</td><td>1.5</td><td>2.5</td><td>mA</td><td></td></tr></table>", "page_idx": 6}, {"type": "text", "text": "2.4. 硬件原理框图", "text_level": 1, "page_idx": 6}, {"type": "table", "img_path": "images/332ebfce56207c1d6136b07f02a79de1dd26d1ecfe42b1cbc749339542b81eed.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-6</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 6}, {"type": "image", "img_path": "images/0f6b4f1448d9f5dc75b3c2b6741fc7fd662c72d63f97a258559829bfa3f31221.jpg", "image_caption": ["Figure 2.4-1 硬件原理框图"], "image_footnote": [], "page_idx": 7}, {"type": "text", "text": "2.5. 系统框图", "text_level": 1, "page_idx": 7}, {"type": "table", "img_path": "images/50e2ae53280311371c1ec2e9f738560d7c7ebc7c52ac47efb3c75421e290d15d.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 7</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 7}, {"type": "text", "text": "EVW 在整车端供电为 IG电，即在整车进入休眠之后水阀模块供电会被切断。", "page_idx": 7}, {"type": "image", "img_path": "images/b11bef1e11c92d931492249532ca18a83dba41c5ed596a8ad5799e7653c3aa34.jpg", "image_caption": ["Figure 2.5-1 系统框图"], "image_footnote": [], "page_idx": 7}, {"type": "text", "text": "3. 功能模块", "text_level": 1, "page_idx": 7}, {"type": "text", "text": "3.1. 电源检测及电源模式", "page_idx": 7}, {"type": "table", "img_path": "images/2b2fd345ee80de98414a9d8fcb93f423d25c2f1f8d96ecfb3b5e007191fe786a.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-S0R-8</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 7}, {"type": "text", "text": "EWV模块保持对电源电压实时检测，并能在各种电压范围内自动进入相应的工作（电压）模式，工作（电压）模式定义如下：", "page_idx": 8}, {"type": "text", "text": "➢电压A模式：即正常模式，EWV所有功能（包括负载控制、通讯、信号采集）均正常工作；  \n$\\gtrdot$ 电压B模式：此模式除了负载不能工作外，其余（通信与信号采集）模块均正常。", "page_idx": 8}, {"type": "table", "img_path": "images/df06776124be358a9ae196b8aec0e71edf565679358f8844133a781a6b6397d8.jpg", "table_caption": ["表 3.1-1 电源电压模式对应电压值定义"], "table_footnote": [], "table_body": "<table><tr><td>No.</td><td>电源电压模式</td><td>低电压区间</td><td>高电压区间</td></tr><tr><td>1</td><td rowspan=\"2\">电压A模式</td><td>Enter: ≥9.0V，&gt;500ms</td><td>Enter: ≤16.0V，&gt;500ms</td></tr><tr><td>2</td><td>Exit: ≤8.5V，&gt;500ms</td><td>Exit：≥16.5V，&gt;500ms</td></tr><tr><td>3</td><td rowspan=\"2\">电压B模式</td><td>Enter：≤8.5V, &gt;500ms</td><td>Enter:≥16.5V, &gt;500ms</td></tr><tr><td>4</td><td>Exit：≥9.0V, &gt;500ms</td><td>Exit：≤16.0V，&gt;500ms</td></tr></table>", "page_idx": 8}, {"type": "text", "text": "备注：", "page_idx": 8}, {"type": "text", "text": "✓以上电压检测精度均定义为 $\\cdot$ ；  \n$\\checkmark$ 以上电压参数中已包含回差（hysteresis）定义；  \n$\\cdot$ 在输出禁止条件消失 $5 0 0 \\mathsf { m s }$ 后，应在 $\\_$ 内重新使输出。", "page_idx": 8}, {"type": "text", "text": "3.2. LIN 通信", "text_level": 1, "page_idx": 8}, {"type": "table", "img_path": "images/d62fc785f0b7bf26d5066f85a818066c8b3cb0b3e7bee0e4d7824a43761f78de.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-9</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 8}, {"type": "text", "text": "三通水阀与整车 VCU 通信方式为 LIN，其主要包括：", "page_idx": 8}, {"type": "text", "text": "➢ LIN 波特率为：19.2kbit/s；  \n➢ LIN 总线接口执行标准参考 LIN2.1；  \n➢三通水阀在整车端作为从节点；  \n$\\gtrdot$ LIN 通信相关功能描述、功能定义参考《SAIC_TWV_3WAY_LIN_Matrix_A1-2025.03.10》。", "page_idx": 8}, {"type": "text", "text": "3.2.1. 水阀接收报文", "text_level": 1, "page_idx": 8}, {"type": "table", "img_path": "images/71f0b955e095ae33b5414e59b3ce5c7574a1aee1d24a45ee16a830115eff67f4.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 10</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 8}, {"type": "text", "text": "整车在非休眠模式下，EWV 会接收到下列 LIN 报文：", "page_idx": 8}, {"type": "table", "img_path": "images/e22b5c04c205f896c175982a0809b56afcff4942a2ff3b6b6693efe2b2e23bee.jpg", "table_caption": ["表 3.2-1 EWV 接收报文"], "table_footnote": [], "table_body": "<table><tr><td>No.</td><td>LIN Msg Name</td><td>ID(Raw)</td><td>Length(Byte)</td><td>Time</td><td>Description</td></tr><tr><td>1</td><td>Valve_Cmd</td><td>0x22</td><td>8</td><td>20ms</td><td rowspan=\"2\">VCU请求水阀运行报文信息</td></tr><tr><td>2</td><td>MasterReq</td><td>0x3C</td><td>8</td><td>Event</td></tr></table>", "page_idx": 8}, {"type": "table", "img_path": "images/7d66b3e67c7c2ea16ac985ff9c1fd26f94bd2e8c8ecbd3a4eab3ced9dced2607.jpg", "table_caption": ["表 3.2-2 EWV 接收报文信息"], "table_footnote": [], "table_body": "<table><tr><td>ID</td><td>Signal Name</td><td>Signal Description</td><td>Start Bit</td><td>Bit Length</td><td>Signal Value Description</td></tr><tr><td>0x22</td><td>ECC_TWV_PosSet</td><td>水阀目标位置请求</td><td>24</td><td>8</td><td>PH=INT*0.4(%) nvalid:0xFB~OxFF</td></tr></table>", "page_idx": 8}, {"type": "table", "img_path": "images/6453aef48f1fe153138750bdf29bf6494499e5897e8bef1546a353601a447fcc.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>鹏翎</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>", "page_idx": 9}, {"type": "table", "img_path": "images/22166ab0f29f53e8ac3d7dad49359d9aadcf10ec1f2691760c3420be870237c1.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td></td><td></td><td>水阀使能信号</td><td>32</td><td>1</td><td></td></tr><tr><td>0x3C</td><td>MasterReqMsg</td><td>诊断请求帧</td><td>0</td><td>64</td><td></td></tr></table>", "page_idx": 9}, {"type": "text", "text": "3.2.2. 水阀反馈报文", "text_level": 1, "page_idx": 9}, {"type": "table", "img_path": "images/b733f61e104d5972cab19a4f0695167a29bca1e1a85f08dcfec585f4335f040b.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 11</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 9}, {"type": "text", "text": "整车在非休眠模式下，EWV 会接收到下列 LIN 报文：", "page_idx": 9}, {"type": "table", "img_path": "images/d813eae706a805c69f54c10b5f1fb4dc108664de648ffa81006e872a86c622ef.jpg", "table_caption": ["表 3.2-3 EWV 反馈报文"], "table_footnote": [], "table_body": "<table><tr><td>No.</td><td>LIN Msg Name</td><td>ID(Raw)</td><td>Length(Byte)</td><td>Time</td><td>Description</td></tr><tr><td>1</td><td>Valve_Rsp</td><td>0x24</td><td>8</td><td>20ms</td><td>EWV反馈水阀运行报文信息</td></tr><tr><td>2</td><td>SlaveResp</td><td>0x3D</td><td>8</td><td>Event</td><td></td></tr></table>", "page_idx": 9}, {"type": "table", "img_path": "images/db4a9ca477062d04ad506664c9315d3814f1732a4eb05fbbb836ae6d18bab6e4.jpg", "table_caption": ["表 3.2-4 EWV 反馈报文信息"], "table_footnote": [], "table_body": "<table><tr><td>ID</td><td>Signal Name</td><td>Signal Description</td><td>Start Bit</td><td>Bit Length</td><td> Signal Value Description</td></tr><tr><td>0x24</td><td>TWV_Resp_Error</td><td>LIN通信错误</td><td>0</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_OverVoltage Flt</td><td>水阀过压故障</td><td></td><td></td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_OverCurrent</td><td>水阀过流故障</td><td>2</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_OverTempFI t</td><td>水阀过温故障</td><td>3</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_UnderVolta geFlt</td><td>水阀欠压故障</td><td>4</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TWV_Initsta</td><td>水阀始标</td><td>5</td><td>1</td><td>Ox1:Have nilized</td></tr><tr><td>TWV_StallFIlt</td><td>水阀堵转故障</td><td>6</td><td>1</td><td>0x0:Normal 0x1:Fail</td></tr><tr><td>TwwMotorsta</td><td>水阀电机状态</td><td>7</td><td>1</td><td>0x0:Motor Running 0x1:Motor Stop</td></tr><tr><td>TWV_RealSta</td><td>水阀位置反馈</td><td>8</td><td>8</td><td>PH=INT*0.4(%) Invalid:0xFB~0xFF</td></tr><tr><td>0x3D</td><td>SlaveRespMsg</td><td>诊断反馈帧</td><td>0</td><td>64</td><td></td></tr></table>", "page_idx": 9}, {"type": "text", "text": "3.2.3. 诊断功能", "text_level": 1, "page_idx": 9}, {"type": "table", "img_path": "images/09a698ec57aa7022eb6dd72af1e3f293394f4bffc121458a64afd16e77f295af.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 12</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 9}, {"type": "table", "img_path": "images/aa6b7cb24355babb5e633a2046e9364fbdba3f3faebf9fdb9ad9af604f6be74e.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>鹏翎 INGI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>", "page_idx": 10}, {"type": "text", "text": "整车 VCU可以通过 $0 \\times 3 0 < / 0 \\times 3 0$ 报文读取 EWV软件/硬件版本信息以及控制水阀进行自校正功能。", "page_idx": 10}, {"type": "text", "text": "3.2.3.1. 诊断报文协议定义 ", "text_level": 1, "page_idx": 10}, {"type": "table", "img_path": "images/e02f3578f7ce1ef32f964101680d1eb89af6b4fa13995cff4a29575ed422e6e9.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 13</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 10}, {"type": "table", "img_path": "images/aeece0dfc9fc8df6382f2721762c2e856357665a8605b260cd43791966f61826.jpg", "table_caption": ["表 3.2-5VCU 请求报文信息定义"], "table_footnote": [], "table_body": "<table><tr><td>ID</td><td>Master ReqBO</td><td>Master ReqB1</td><td>Master ReqB2</td><td>Master ReqB3</td><td>Master ReqB4</td><td>Master ReqB5</td><td>Master ReqB6</td><td>Master ReqB7</td></tr><tr><td>0x3C</td><td>NAD</td><td>PCI</td><td>SID</td><td>子服务</td><td>Suppier</td><td>Suplierr</td><td>Functinn</td><td>Functionn</td></tr></table>", "page_idx": 10}, {"type": "table", "img_path": "images/46c6d8f30e5594be15aa189b6a96fce0205529ee310252fd97218c90bb425535.jpg", "table_caption": ["表 3.2-6EWV 反馈报文信息定义"], "table_footnote": ["注 1： $R S 1 D = S 1 D + 0 \\times 4 0$ "], "table_body": "<table><tr><td>ID</td><td>Slave ResB0</td><td>Slave ResB1</td><td>Slave ResB2</td><td>Slave ResB3</td><td>Slave ResB4</td><td>Slave ResB5</td><td>Slave ResB6</td><td>Slave ResB7</td></tr><tr><td>0x3D</td><td>NAD</td><td>PCI</td><td>RSID注1</td><td>DO</td><td>D1</td><td>D2 -</td><td>D3</td><td>D4</td></tr></table>", "page_idx": 10}, {"type": "table", "img_path": "images/317b3d1cffe0742a3ddbcb6e80ce91801388bc66989be85a8e3c3c5b847058f3.jpg", "table_caption": ["表 3.2-7 参数列表"], "table_footnote": [], "table_body": "<table><tr><td>序号</td><td>符号</td><td>定义参数</td><td>备注</td></tr><tr><td>1</td><td>NAD</td><td>0x32</td><td></td></tr><tr><td>2</td><td>PCI</td><td>0x06</td><td></td></tr><tr><td>3</td><td>SID</td><td>0xB2/0xB4</td><td></td></tr><tr><td>4</td><td>RSID</td><td>0xF2/0xF4</td><td></td></tr><tr><td>5</td><td>Supplier_ID</td><td>0x0000</td><td>暂定0000</td></tr><tr><td>6</td><td>Function_ID</td><td>0x0000</td><td>暂定0000</td></tr></table>", "page_idx": 10}, {"type": "text", "text": "3.2.3.2. 软件/硬件版本号读取", "text_level": 1, "page_idx": 10}, {"type": "table", "img_path": "images/d43b09251dfbab3685da7263afe45ed51ba221a24d06f734c89f30552e3b9ba1.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 14</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 10}, {"type": "text", "text": "三通水阀可以通过 $0 \\times 3 0 < / 0 \\times 3 0$ 服务帧和 $0 \\times 8 4$ 诊断服务帧读取软件/硬件版本号。帧结构定义如下：", "page_idx": 10}, {"type": "table", "img_path": "images/70390d55935ca864b62d0b4391385e46268f1e620503a92d955e825c162576b9.jpg", "table_caption": ["表 3.2-8 VCU 请求报文信息"], "table_footnote": [], "table_body": "<table><tr><td>ID</td><td>Master ReqBO</td><td>Master ReqB1</td><td>Master ReqB2</td><td>Master ReqB3</td><td>Master ReqB4</td><td>Master ReqB5</td><td>Master ReqB6</td><td>Master ReqB7</td></tr><tr><td>0x3C</td><td>0x32</td><td>0x06</td><td>0xB4</td><td>0x00</td><td>0x00</td><td>0x00</td><td>0x00</td><td>0x00</td></tr></table>", "page_idx": 10}, {"type": "table", "img_path": "images/163c1958b4c5ae9d66c701ee041c271f5a3db29220903db4dd104cf7792ca3b3.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>鹏翎 LING</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>", "page_idx": 11}, {"type": "table", "img_path": "images/8bd2f8d44b8b7f472d247199addeaf2918737e631e0a383109be24987fcf20a5.jpg", "table_caption": ["表 3.2-9 EWV 反馈报文信息"], "table_footnote": [], "table_body": "<table><tr><td>ID</td><td>Slave ResBO</td><td>Slave ResB1</td><td>Slave ResB2</td><td>Slave ResB3</td><td>Slave ResB4</td><td>Slave ResB5</td><td>Slave ResB6</td><td>Slave ResB7</td></tr><tr><td>0x3D</td><td>0x32</td><td>0x06</td><td>0xF4</td><td>软件 主版本</td><td>软件 次版本</td><td>硬件 主版本</td><td>硬件 次版本</td><td>OxFF</td></tr></table>", "page_idx": 11}, {"type": "text", "text": "其中定义如下：", "page_idx": 11}, {"type": "text", "text": "➢ 软件主版本：按照A,B,C,D 进行，对应的值参考 ASIIC值表示；如 $\\mathsf { A } \\mathsf { - } \\mathsf { 4 1 }$ ， $\\tt B \\mathrm { \\cdot } > 4 2$ ， $c \\mathord { \\mathrm { \\sim } } 4 3$ 以此类推；", "page_idx": 11}, {"type": "text", "text": "软件次版本：按照 0,1,2,3…9编码；", "page_idx": 11}, {"type": "text", "text": "$\\gtrdot$ 硬件主版本：按照A,B,C,D 进行，对应的值参考 ASIIC值表示；如 $\\mathsf { A } \\mathsf { - } \\mathsf { 4 1 }$ ， $\\mathsf { B } \\mathrm { - } \\mathsf { 4 } 2$ ，C->43 以此类推；", "page_idx": 11}, {"type": "text", "text": "➢ 硬件次版本：按照 0,1,2,3…9 编码。", "page_idx": 11}, {"type": "text", "text": "3.2.3.3. 水阀自标定控制", "text_level": 1, "page_idx": 11}, {"type": "table", "img_path": "images/2690da55040a54d45bf2cd59fcddb35f506d9f7cd9cc788958c24c484c1286c9.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 15</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 11}, {"type": "text", "text": "三通水阀可以通过 $0 \\times 3 0 < / 0 \\times 3 0$ 服务帧和0xB2 诊断服务帧、 $\\mathtt { 0 } \\mathtt { x } \\mathtt { 3 0 }$ 子服务帧控制水阀进行自标定操作。帧结构定义如下：", "page_idx": 11}, {"type": "table", "img_path": "images/198d3beb8c87484d747e05dc30d2e6d0df06709fc0ac42a8544833cb42bea3dc.jpg", "table_caption": ["表 3.2-10 VCU 请求报文信息"], "table_footnote": [], "table_body": "<table><tr><td>ID</td><td>Master ReqBO</td><td>Master ReqB1</td><td>Master ReqB2</td><td>Master ReqB3</td><td>Master ReqB4</td><td>Master ReqB5</td><td>Master ReqB6</td><td>Master ReqB7</td></tr><tr><td>0x3C</td><td>0x32</td><td>0x06</td><td>0xB2</td><td>0x30</td><td>0x00</td><td>0x00</td><td>0x00</td><td>0x00</td></tr></table>", "page_idx": 11}, {"type": "table", "img_path": "images/5e048f26ba0d991e00a7cd4052e0c4ab800ab95145827455631b6e10f45de91a.jpg", "table_caption": ["表 3.2-11 EWV 反馈报文信息"], "table_footnote": [], "table_body": "<table><tr><td>ID</td><td>Slave ResBO</td><td>Slave ResB1</td><td>Slave ResB2</td><td>Slave ResB3</td><td>Slave ResB4</td><td>Slave ResB5</td><td>Slave ResB6</td><td>Slave ResB7</td></tr><tr><td>0x3D</td><td>0x32</td><td>0×06</td><td>0xF2</td><td>0x01注2</td><td>0x00</td><td>0x00</td><td>0x00</td><td>OxFF</td></tr></table>", "page_idx": 11}, {"type": "text", "text": "注 2：", "page_idx": 11}, {"type": "text", "text": "$\\gtrdot$ 当值反馈为 $0 \\times 1$ 时，表示水阀自标定成功；  \n$\\gtrdot$ 当值反馈为 $0 { \\times } 0$ 时，表示水阀自标定失败。", "page_idx": 11}, {"type": "text", "text": "3.3. 自校正", "text_level": 1, "page_idx": 11}, {"type": "table", "img_path": "images/a64afa10511c59be503dc8a53fc561362e5f32c1f1755bf5f0d19ad0e659c290.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 16</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 11}, {"type": "text", "text": "三通水阀内部电机采用的 BLDC 电机，由于其力矩较小，在长时间运转过程中可能存在丢步的风险。故增加自校正主要是为了匹配不同阀体的差异以及消除运行一段时间后而出现的误差。", "page_idx": 11}, {"type": "table", "img_path": "images/53bcf92f0545878d67c18a21a63e8d78835009be3be493be4715641b9a95c784.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>鹏翎 NE</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>", "page_idx": 12}, {"type": "text", "text": "3.3.1. 自校正条件", "text_level": 1, "page_idx": 12}, {"type": "table", "img_path": "images/dbfcecfc158025a171bebb479144160b07de1ff472f0107a8831564c3e1a6b30.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 17</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 12}, {"type": "text", "text": "三通水阀在满足下列条件情况下才能进行自校正：", "page_idx": 12}, {"type": "text", "text": "EWV重新上电或休眠唤醒后存在下列情况之一：  \n➢ VCU 请求水阀自标定控制（0x3C / 0xB2 / 0x30）  \n➢EWV上个周期异常掉电；  \n➢ EWV 行程达到 100,000 步之后；  \n➢EWV上个周期存在故障（过流、开路、堵转）且未恢复。", "page_idx": 12}, {"type": "text", "text": "3.3.2. 自校定策略", "text_level": 1, "page_idx": 12}, {"type": "table", "img_path": "images/be1ec0a2f01bcfbe44f26dfb26699e5fc29d240bb9da773ec1396cc59aa861b3.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 18</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 12}, {"type": "text", "text": "a) 起始点位置定位", "page_idx": 12}, {"type": "text", "text": "➢ 水阀控制电机按照正转方向进行运动；记录电机因为产生堵转而停止转动时所处的位置，并标记此处位置为起始位置，并记录此处 Hall脉冲为 0。", "page_idx": 12}, {"type": "text", "text": "➢ 此阶段电机转速为 1000rpm（待定）运行。", "page_idx": 12}, {"type": "text", "text": "b) 电机位置校正", "page_idx": 12}, {"type": "text", "text": "➢ 电机反转方向按照 1000rpm 运行，记录电机因为产生堵转而停止转动时所处的位置，并标记此处位置为终止位置；并设置此时 Hall 累积反馈的总脉冲数为的 $N _ { a l l }$ 。", "page_idx": 12}, {"type": "text", "text": "➢ 其中总脉冲数中包好了起始位置（ ${ \\mathbf { } } \\cdot N _ { 1 }$ ）和终止位置（ $( N _ { 2 }$ ）处的机械总误差步数 $N _ { E }$ ，电机实际旋转总步数 $N _ { n o m }$ ，电机旋转总角度 $\\theta _ { n o m }$ ， 电机实际运行步数 $N _ { a c t }$ ，实际运行角度 $\\theta _ { a c t }$ 计算公式如下：", "page_idx": 12}, {"type": "text", "text": "$\\begin{array} { r l } & { \\theta _ { a c t } = \\frac { N _ { a c t } } { N _ { n o m } } * \\theta _ { n o m } } \\\\ & { N _ { n o m } = N _ { a l l } - N _ { E } } \\\\ & { N _ { E } = N _ { 1 } + N _ { 2 } } \\end{array}$ 公式一公式二公式三", "page_idx": 12}, {"type": "text", "text": "版本：", "page_idx": 13}, {"type": "image", "img_path": "images/140fb04c16fe7b43a0e7fe40549dd8ff8e358a465828996b0a33ae8207986d81.jpg", "image_caption": ["Figure 3.3-1 水阀自校正示意图"], "image_footnote": [], "page_idx": 13}, {"type": "text", "text": "c) 自校正时电机堵转判定条件", "page_idx": 13}, {"type": "text", "text": "水阀自校正运行过程中需要实时监测电机运行电流，当监测到：", "page_idx": 13}, {"type": "text", "text": "$\\curlyeqsucc$ 电机运行电流 $I _ { a c t } { > } 0 . 2 5 \\mathsf { A }$ ，持续时 $\\mathsf { t } { > } 1 0 0 \\mathsf { m s }$ ；  \n➢ 连续检测到Hall信号未发生变化，且持续时间超过 100ms。  \n满足以上条件则判定为自校正时堵转状态。", "page_idx": 13}, {"type": "text", "text": "备注：以上具体参数需后期标定确认。", "page_idx": 13}, {"type": "text", "text": "3.4. 三通水阀运行", "text_level": 1, "page_idx": 13}, {"type": "table", "img_path": "images/8cabd94221be0305b5e65780afb4c0515d920770d288787c20e3a9efd983f7bb.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 19</td><td>SAIC_EWV_3WAY_ RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 13}, {"type": "text", "text": "三通水阀运行需要根据整车 VCU 指令，按照特定的方向及特定的行程运行；其要求如下：", "page_idx": 13}, {"type": "text", "text": "最大角度运行时间 ${ < } 8 5$ ；  \n集成水阀正常运行时：不同电压下按照相同的转速运行（ $2 0 ^ { \\circ } / s ,$ ）：✓ 电源供电电压：9V\\~16V。  \n水阀自标定过程中暂定全速运行，后期以实际工况调整。", "page_idx": 13}, {"type": "text", "text": "3.5. 三通水阀运行模式", "text_level": 1, "page_idx": 13}, {"type": "table", "img_path": "images/87f275d2b6fbd95e212ad84421957531a44dd91aa6f4f4f3e01912bf731d6149.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-20</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 13}, {"type": "text", "text": "三通水阀为比例水阀，其中：", "page_idx": 13}, {"type": "text", "text": "$0 \\%$ ： $_ { 2 \\cdot > 1 }$ 连通， $3 \\cdot > 1$ 截止；$\\triangleright 0 \\% \\sim 1 0 0 \\%$ ： $_ { 2 \\cdot > 1 }$ ， $3 \\cdot > 1$ 比例连通；$100 \\%$ ： $_ { 2 \\cdot > 1 }$ 截止， $3 \\cdot > 1$ 连通。", "page_idx": 13}, {"type": "table", "img_path": "images/82897fb906ec1301ccd85b779e5c4e613d6ccbbb0945a3ba20a10215ccbceddf.jpg", "table_caption": ["表 3.5-1 三通水阀运行模式及角度对应关系"], "table_footnote": [], "table_body": "<table><tr><td>序列</td><td>模式</td><td>三通水阀比率 (%)</td><td>角度范围 （°)</td><td>脈冲数</td><td>示意图</td><td>备注</td></tr></table>", "page_idx": 13}, {"type": "text", "text": "版本：C", "page_idx": 14}, {"type": "table", "img_path": "images/2310b91a8bdffb7ce7d440f9be0d61ae6d88fe127ac362587131d2d6d34c8943.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>1</td><td>起始端限位</td><td>/</td><td>0°</td><td>0</td><td></td><td></td></tr><tr><td>2</td><td>/</td><td>0%</td><td>10°</td><td>74</td><td>B O</td><td></td></tr><tr><td>3</td><td></td><td>0%~100%</td><td>10°~80°</td><td></td><td>3* 2</td><td></td></tr><tr><td>4</td><td>/</td><td>100%</td><td>80° -</td><td>590</td><td>3 2</td><td></td></tr><tr><td>5</td><td>终止端限位</td><td></td><td>90°</td><td>664</td><td></td><td></td></tr></table>", "page_idx": 14}, {"type": "text", "text": "3.6. 位置检测", "text_level": 1, "page_idx": 14}, {"type": "table", "img_path": "images/716aaf661d55cd7b4dd2c69f33ce1a77901a30c411f665d8b7f2cbf4e64dea0f.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 21</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 14}, {"type": "text", "text": "水阀内部电机运行状态采用相对位置传感器（开关 Hall）来反馈阀芯的角度。", "page_idx": 14}, {"type": "image", "img_path": "images/a1538b5d436d58decf75c6f0b0488a514525bf20074f941f8910a3fb8eaa66fa.jpg", "image_caption": ["Figure 3.6-1 Hall 硬件采集电路原理图"], "image_footnote": [], "page_idx": 14}, {"type": "text", "text": "输出旋转角度 $\\theta _ { a c t }$ 与脉冲数 $N _ { a c t }$ 的关系式如下：", "page_idx": 14}, {"type": "table", "img_path": "images/4ea64dee5d97a7a0813bf2996132498f506577b53a69beea55cf71c6b8f98c15.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>鹏翎 NE</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>", "page_idx": 15}, {"type": "equation", "img_path": "images/f2bcd4a37e7d72668c75c645b7d21796a500e3e0b42b036126051a680b5c03c6.jpg", "text": "$$\n\\begin{array} { r } { N _ { a c t } = \\frac { \\dot { \\theta _ { a c t } ^ { \\circ } } } { 3 6 0 ^ { \\circ } } * \\frac { 1 } { K } * n * 2 } \\end{array}\n$$", "text_format": "latex", "page_idx": 15}, {"type": "text", "text": "公式四", "page_idx": 15}, {"type": "text", "text": "其中： $\\theta _ { a c t }$ 代表阀芯旋转角度，360 代表旋转一圈为 $3 6 0 ^ { \\circ }$ ，K 为齿轮的减速比，n 为磁铁极对数，2 表示为磁铁磁极变化 1次时 Hall高低变化2 次。", "page_idx": 15}, {"type": "text", "text": "3.7. 诊断功能", "text_level": 1, "page_idx": 15}, {"type": "table", "img_path": "images/ee36bc1a96648c9bbd3e2525526e2d80fe0021e29f51d835fd9d8e60b825d5bb.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-22</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 15}, {"type": "table", "img_path": "images/c02ac74b82eeec9f78985367549abbbbb2c9a23038f99c15eef8759231e3161a.jpg", "table_caption": ["表 3.7-1 水阀故障诊断列表"], "table_footnote": [], "table_body": "<table><tr><td>信号定义</td><td>故障类型</td><td>故障优先级</td><td>故障判定前置条件</td><td>备注</td></tr><tr><td rowspan=\"7\">电机诊断 Fault_Signal</td><td>过流故障</td><td>1</td><td>全程检测</td><td></td></tr><tr><td rowspan=\"3\">堵转故障</td><td>2（电机堵转）</td><td>全程检测</td><td></td></tr><tr><td>2（阀芯角度低于量程）</td><td>自校正检测</td><td></td></tr><tr><td>2 （传感器异常）</td><td>全程检测 - -</td><td></td></tr><tr><td>过温故障</td><td>3</td><td>全程检测 -</td><td></td></tr><tr><td rowspan=\"2\">开路故障</td><td>4（电机开路）</td><td>全程检测</td><td></td></tr><tr><td>4（阀芯角度超量程）</td><td>自校正检测</td><td></td></tr><tr><td rowspan=\"2\">VoltageErr</td><td>过压故障</td><td>-</td><td>全程检测</td><td></td></tr><tr><td>欠压故障</td><td></td><td>全程检测</td><td></td></tr><tr><td>RespError</td><td>LIN通信故障</td><td></td><td>全程检测</td><td></td></tr></table>", "page_idx": 15}, {"type": "text", "text": "3.7.1. 电机诊断策略（Fault_Signal）", "text_level": 1, "page_idx": 15}, {"type": "text", "text": "3.7.1.1. 过流故障（OverCurrent）", "text_level": 1, "page_idx": 15}, {"type": "table", "img_path": "images/a2fa49f851f2245a57c8c26130822cfbe2873a960db3b3fd590dcf53ce9b8837.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-23</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 15}, {"type": "text", "text": "线圈短路故障是由于电机 PIN针短接在一起或者电机 PIN对电源/对地短路而引起的故障。", "page_idx": 15}, {"type": "table", "img_path": "images/595119a982d86c6927f8542592282d4bf83b24ddda2d0605b268cfac2c4727b5.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>故脉</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复</td><td>重启策略</td></tr><tr><td>线图</td><td>全程检测</td><td>MOs管降</td><td>1)线圈短路故障判定成立后水 阀停转，2s后尝试重启； 2) 第5次重启失败之后，EWV 反线圈短路故障，且再 3) 直至重启合计10次，则在本 工作周期内不在执行重启，同 时不响应VCU的位置请求；同 时位置反馈信息为当前位置。</td><td>VDs&lt;1.8V</td><td>重启次数及时间： 每次问满， 停止重启。</td></tr></table>", "page_idx": 15}, {"type": "text", "text": "备注：软件部分需求设置 HBSTATUS(0x06)分别开启 $\\mathsf { A } / \\mathsf { B } / \\mathsf { C }$ 通道 half bridge 过流保护。", "page_idx": 16}, {"type": "text", "text": "其中：", "page_idx": 16}, {"type": "image", "img_path": "images/933cd3dd5f4530dc96fb44597a604bb9ae9c02be843ad3dc8672ff84b6b58a16.jpg", "image_caption": [], "image_footnote": [], "page_idx": 16}, {"type": "text", "text": "当检测到通过 MOS 管的 $\\mathsf { V } _ { \\mathsf { D } \\mathsf { S } } { \\mathsf { - } } 1 . 8 \\mathsf { V } \\mathsf { S }$ 时，MCU 会关断 MOS 输出，以达到保护功率器件不被损坏；此时 MOS的最大保护电流 $I _ { M O S M A X }$ 与最小保护电流 $. I _ { M O S M I N }$ 关系式如下：", "page_idx": 16}, {"type": "equation", "img_path": "images/f470d3ec3b94edb849b1bc88007a43523e8510abc6cb2611069a8d844796ced0.jpg", "text": "$$\n\\begin{array} { r } { I _ { M O S M I N } = \\frac { V _ { R E F } } { G _ { A I N ^ { * R } M O S M A X } } = \\frac { 1 . 8 } { 1 * 1 . 1 5 } = 1 . 5 7 A } \\end{array}\n$$", "text_format": "latex", "page_idx": 16}, {"type": "text", "text": "3.7.1.2. 堵转故障（Stall）", "text_level": 1, "page_idx": 16}, {"type": "table", "img_path": "images/342d0109d51ec3529bc6569c3fb45fbdc211563af3d293a61375a28a58d339e5.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 24</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 16}, {"type": "text", "text": "堵转故障是定义阀体在运行过程中由于阀体堵转、位置传感器异常引起的故障。", "page_idx": 16}, {"type": "table", "img_path": "images/2123ec4f1a26e0dece30310403d6e17ad0895115da5c5910034ca061bee25871.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>故障</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td>堵转 故障</td><td>电机堵转</td><td>电机运行电流I</td><td>1)堵转判定成立后 水阀停止运行，2s后 尝试重启。 2) 第5次重启失败之 后，EWV开始上报堵 转故障，并再次执行 重启； 3)直至重启合计10 次，则在本工作周期 内不在行向自</td><td>1)电机运行电流 0.1A≤I≤0.4A，持 续&gt; 0ms 以 2)阀体重启成功 后,水阀开始进 行到0°）；运行 到0°后按照VCU 指令运行到目标</td><td>重启次数机时 间： 每次重启间隔 2s；重启10次 无法恢复，则 停止重启。</td></tr></table>", "page_idx": 16}, {"type": "text", "text": "版本：C", "page_idx": 17}, {"type": "table", "img_path": "images/b7292c9f5830fcdcdc51c5c8a841dd5ff491c12c9076dee8a0f9bd3db1228952.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>位置传感器異常 （包含传感器供电 异常，传感器异</td><td>电机行过 电平信号不 转，且持续时间</td><td rowspan=\"2\">止运行，间隔2s后尝 试重次重启失败之</td><td>反感 2)阀体重启成功</td><td rowspan=\"2\">重启次数机时</td></tr><tr><td>阀芯角度低于量 程 （位置传感器反馈 的总脉冲数Naı小于 行程）</td><td>重启； 位置传感器反 馈脉冲数Nall</td><td>数605≤ Nau ≤ 3)直至重启合计10 698； 次，则在本工作周期 2)阀体恢复正常 内不在执行重启；同 后，EWV按照VCU指</td></tr></table>", "page_idx": 17}, {"type": "text", "text": "3.7.1.3. 过温故障（OverTempShutdown）", "text_level": 1, "page_idx": 17}, {"type": "table", "img_path": "images/8bb11e86d42602daf16e19f87c30fbaa22d112a93097afcfa439f3c976649a05.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 25</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 17}, {"type": "text", "text": "过温故障是定义阀体驱动芯片温度内部超过阀值而引起的故障。", "page_idx": 17}, {"type": "table", "img_path": "images/d30fff72024c09ca95b29bcbfe4b19c8bbdcb99879d65408267ae7dbfa6c0eec.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>故际</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td>过温</td><td>MCU內部温度过温</td><td></td><td>1)过温关断判定成立 后即信报过温关 )输出关断。 2)</td><td>M内部温度 2)持续时间t&gt;20s</td><td>无</td></tr></table>", "page_idx": 17}, {"type": "text", "text": "备注：过温关断故障恢复后 EWV 需立即按照 VCU 指令从关断位置运行到最新的目标位置，若指令位置与当前位置一致则保持静止。", "page_idx": 17}, {"type": "text", "text": "3.7.1.4. 开路故障（Coil_Open）", "text_level": 1, "page_idx": 17}, {"type": "table", "img_path": "images/6e388600efa46b18174856dc271d8b7b50cd844ed513a0e66f902a1dc010e36c.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR-26</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 17}, {"type": "text", "text": "开路故障是定义阀体在运行过程中由于阀体断齿、电机开路引起的故障。", "page_idx": 17}, {"type": "table", "img_path": "images/caf93af94583976528ae71afa7a70c860af231aad513e29501aad4dbf4b1854a.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>故际</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td rowspan=\"2\">开路降</td><td>电机开路</td><td>电机运行电流I&lt; 80mA，持续t&gt;</td><td rowspan=\"2\">1)开路判定成立后水 阀停转，2s后尝试重 启；</td><td>1)电机运行电流 0.1A≤I≤0.5A，持续 500ms以上；</td><td>重启次数 机时间：</td></tr><tr><td>程）</td><td>周期内停止恢复。</td><td>到目标位置。</td><td></td></tr></table>", "page_idx": 17}, {"type": "table", "img_path": "images/6f4a3029a6e5344732e86aa12b5cb8ff797a45ec1ab9a463197e6b8d251fc7c2.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>鹏翎 NGI</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>", "page_idx": 18}, {"type": "text", "text": "3.7.2. 水阀电压故障（VoltageError）", "text_level": 1, "page_idx": 18}, {"type": "table", "img_path": "images/55e678ecafed0ac7597d0d2d74895e17770c9bcd6c81ef401d4d6af758d2e4b6.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 27</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 18}, {"type": "text", "text": "水阀电压故障是定义由于整车供电异常，导致水阀供电端电压高于其正常电压范围而定义的故障。", "page_idx": 18}, {"type": "table", "img_path": "images/334aeb95d386bac724b8f133dcb195d93819f74e9dfd9e800f76bacf105846e6.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>故障 名称</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td rowspan=\"2\">电压 故障</td><td>(0 ae)</td><td></td><td>满压B模</td><td>压落过持</td><td>无</td></tr><tr><td>(0)</td><td></td><td>满压B</td><td></td><td>无</td></tr></table>", "page_idx": 18}, {"type": "text", "text": "备注：EWV 在发生电压故障，阀体停止在当前位置，在电压故障恢复后 EWV 需立即按照 VCU指令从当前位置运行到最新的目标位置，若指令位置与当前位置一致则保持静止。", "page_idx": 18}, {"type": "text", "text": "3.7.3. 过温警告（TempWarn）", "text_level": 1, "page_idx": 18}, {"type": "table", "img_path": "images/f158495094c4070530cf27c29c37b31f9abbc155cfd151532d73a5980e1557c1.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC EWV 3WAY SOR 28</td><td>SAIC_EWV_3WAY_RFQ A1</td><td>Validation test</td></tr></table>", "page_idx": 18}, {"type": "table", "img_path": "images/90cefa3cc031d4c05095d4405acf9d27bb2f977d458b7dde63be0b5ccf2b0b21.jpg", "table_caption": ["过温警告故障是定义由于 PCBA 发热而导致 MCU 发热严重，为了保护 MCU 而定义的故障。"], "table_footnote": [], "table_body": "<table><tr><td>故障名称</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机製</td><td>重启策略</td></tr><tr><td>过温告</td><td>MCU内部温</td><td>MCU内温</td><td>1)过温警告判定成 行</td><td>)内温度≤125℃</td><td>无</td></tr></table>", "page_idx": 18}, {"type": "text", "text": "3.7.4. LIN 通信故障", "text_level": 1, "page_idx": 18}, {"type": "table", "img_path": "images/5e53c8666d263a02682bd1408dcc0035198025a0a2adaa9ff820cee26dd30e5c.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 29</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 18}, {"type": "text", "text": "LIN 通信故障是指 LIN 总线出现断开，Busoff，短路等情况而定义的故障。", "page_idx": 18}, {"type": "table", "img_path": "images/9304825e0b169164937c2ff92ef4ad8fbd328f8b284074288d71d68b05b84edf.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>故障名称</td><td>故障类型</td><td>故障触发标准</td><td>故障动作</td><td>故障恢复机制</td><td>重启策略</td></tr><tr><td>Comm_Err</td><td>LIN通信故</td><td>BusOff</td><td>1) LIN Busoff判定成 立后上报过LIN通 信信障续时 间&gt;1s后，水阀运 行到安全位置。</td><td></td><td>无</td></tr></table>", "page_idx": 18}, {"type": "table", "img_path": "images/942c3001496c78cdc13ca175ab6ca2672add7c6d7a1b50a0f8886fb11d3c3138.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>鹏翎</td><td>版本：C NO:E4000004-SAIC_EWV_3WAY_BLDC-SOR</td></tr></table>", "page_idx": 19}, {"type": "text", "text": "3.8. E2PROM 数据存储/读写", "text_level": 1, "page_idx": 19}, {"type": "text", "text": "3.8.1. E2PROM 数据存储/读写条件", "text_level": 1, "page_idx": 19}, {"type": "table", "img_path": "images/86872728393d8ee67fa3e89296fb620f31592c4464236802eab0ae29c5a21495.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>MERIT-EWV-3WAY-SOR- 30</td><td>MERIT_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 19}, {"type": "text", "text": "E2PROM 在下列情况下需要进行数据存储；  \n➢ EWV进入休眠模式；  \n➢ EWV异常掉电(当电源电压＜10V时）。  \nE2PROM 在下列情况下需要进行数据读写；  \n➢ EWV上电或者休眠唤醒后；", "page_idx": 19}, {"type": "text", "text": "3.8.2. E2PROM 数据存储/读写策略", "text_level": 1, "page_idx": 19}, {"type": "table", "img_path": "images/e8062aba2b2b3ce9b212a907bfba6c378086081e169e0b0951a424730a0d980c.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 31</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 19}, {"type": "text", "text": "E2PROM主要是用于存储掉电后需要记忆的数据，主要包括下列数据：", "page_idx": 19}, {"type": "table", "img_path": "images/cfad486dee39ef8ab07278de8eb4f8dd8a43c170921b41322d620b59f404d900.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>No.</td><td>数据名称</td><td>符号</td><td>数据格式</td><td>数据范围</td><td>备注</td></tr><tr><td>1</td><td>正常下电标志</td><td>SstateBit</td><td>int</td><td>0~1</td><td>1.此值在休眠时写入的顺序为最后， 即在其余参数写完之后再写入此 值；其中写入的参数为“1”。 2.上电/唤醒后，确认此参数之后需</td></tr><tr><td>2</td><td>水阀自标定Hall总 脉冲数</td><td>NHalAll</td><td>uint16</td><td>0~65,535</td><td>要立即写入参数为“0”。</td></tr><tr><td>3</td><td>水阀运行方向</td><td>Dair</td><td>int -</td><td>0~1</td><td></td></tr><tr><td>4</td><td>水阀运行总行程</td><td>nEWVAIl</td><td>uint32</td><td>0~4,294,967,295</td><td>记录水阀总计运行脉冲数</td></tr><tr><td>5</td><td>水阀故障标志位</td><td>FFaultBit</td><td>int</td><td>0~1</td><td>10：正常</td></tr><tr><td>6</td><td>水阀正向补偿量</td><td>ncw</td><td>uint8</td><td>0~255</td><td></td></tr><tr><td>7</td><td>水阀反向补偿量</td><td>nccw</td><td>uint8</td><td>0~255</td><td></td></tr><tr><td>8</td><td>水阀当前Hal脉冲</td><td>nHallcurrent</td><td>uint16</td><td>0~65,535</td><td></td></tr><tr><td>9</td><td>水阀休眠次数</td><td>nslepptime</td><td>uint8</td><td>0~255</td><td></td></tr></table>", "page_idx": 19}, {"type": "text", "text": "3.9. 安全位置", "text_level": 1, "page_idx": 19}, {"type": "table", "img_path": "images/667f95a8336c39b4ef0e0b96643904ff2af71f22e659ecb57ff72ae1e13031fa.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 32</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 19}, {"type": "text", "text": "安全位置是定义当EWV出现异常之后，为了保证整车安全行驶而定义的位置。", "page_idx": 19}, {"type": "text", "text": "安全位置定义： ReqMovePos =0x7D （开度 $50 \\%$ ：1&2&3 互通）。", "page_idx": 20}, {"type": "text", "text": "3.9.1. 安全工况定义", "text_level": 1, "page_idx": 20}, {"type": "table", "img_path": "images/62bdada3a2cec8c92c8b50c07e2b401589b93b52c09e6ec0c11a620489e7bb65.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 33</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 20}, {"type": "text", "text": "➢ EWV与 VCU通信故障（包括通信故障、通信丢失）；", "page_idx": 20}, {"type": "text", "text": "3.10. 出厂位置定义", "page_idx": 20}, {"type": "table", "img_path": "images/7cbdf2a9ea453eb2b0a56cb9b720215493b6d1f37a25217a52a7239c1e8c3eb6.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 34</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 20}, {"type": "text", "text": "出厂位置是定义为了满足整车防冻液的加注和排气而定义的位置。出厂 位置定义：ReqMovePos $= 0 \\times 7 0$ （开度 $50 \\%$ ：1&2&3 互通）。", "page_idx": 20}, {"type": "text", "text": "3.11. 休眠与唤醒", "text_level": 1, "page_idx": 20}, {"type": "table", "img_path": "images/56356b5ec8b0d2893fdaa456469e4e5d1116845bac166e42dc4975c2cb53bfe3.jpg", "table_caption": [], "table_footnote": [], "table_body": "<table><tr><td>Code</td><td>Cover</td><td>Test Method</td></tr><tr><td>SAIC-EWV-3WAY-SOR- 35</td><td>SAIC_EWV_3WAY_RFQ-A1</td><td>Validation test</td></tr></table>", "page_idx": 20}, {"type": "text", "text": "为了降低功耗并确保系统在需要时快速恢复正常工作状态，有两种基本工作模式：运行（ACTIVE）、睡眠（SLEEP）。其中：以下两个条件有一个成立，立即进入睡眠，静态电流≤100uA。", "page_idx": 20}, {"type": "text", "text": "➢ 接收到 LIN 睡眠指令(ID:0x3C) 0x00 0xFF 0xFF 0xFF 0xFF 0xFF 0xFF 0xFF；  \n➢ 总线 4S 空闲条件成立。  \n满足下面条件，EWV可从睡眠中唤醒。  \n➢ 通讯总线上有任意活动。", "page_idx": 20}]